import { defineStore } from 'pinia';
import type { CartItem, UserStatus } from '~/types/userStatus';
import dateConverter from '~/helpers/dateConverter';

export type SCartState = {
  isCartLoaded: boolean;
  isCartOpen: boolean;
  cartUsedAssembly: boolean;
  cartItems: Array<CartItem>;
} & Record<string, any>;

export const useScartStore = defineStore('scart', {
  state: (): SCartState => ({
    isCartLoaded: false,
    isCartOpen: false,
    previewImgUrl: null,
    cartAssemblyPriceRegionalized: '',
    cartTotalPriceBeforeDiscountRegionalized: '',
    cartTotalPriceRegionalized: '',
    cartUsedPromo: false,
    cartItemsCount: 0,
    shelfItemsCount: 0,
    cartItems: [],
    hasT03: false,
    hasLighting: false,
    promocodeName: '',
    promocodeValueRegionalized: '',
    hasAssemblyPossible: false,
    hasFastTrackPossible: false,
    cartUsedAssembly: false,
    cartUsedFastTrack: false,
    waitingListExpireDate: '',
    T03Available: false,
    orderPricing: {},
    deliveryTime: '',
    isSKU: false,
    totalPrice: null,
    totalPriceBeforeDiscount: null,
    removeItemAvailable: true,
    placedAt: null,
    chosenPaymentMethod: null
  }),
  actions: {
    PROMOCODE_UPDATE (data: any) {
      this.cartTotalPriceBeforeDiscountRegionalized = data.cart_total_price_before_discount;
      this.cartTotalPriceRegionalized = data.cart_total_price;
      this.cartUsedPromo = data.is_active;
      this.promocodeValueRegionalized = data.discount_price;
      this.promocodeName = data.promoCodeName;
      this.orderPricing = data.order_pricing;
      this.totalPrice = data.order_pricing.total_price;
      this.totalPriceBeforeDiscount = data.order_pricing.total_price_before_discount;
    },
    ASSEMBLY_UPDATE (data: any) {
      this.cartUsedAssembly = data.assembly;
      this.cartAssemblyPriceRegionalized = data.assembly_price;
      this.cartTotalPriceBeforeDiscountRegionalized = data.shelf_price;
      this.cartTotalPriceRegionalized = data.total_price;
      this.orderPricing = data.order_pricing;
      this.hasFastTrackPossible = data.is_valid_for_fast_track;
      this.totalPrice = data.order_pricing.total_price;
      this.totalPriceBeforeDiscount = data.order_pricing.total_price_before_discount;
    },
    STATUS_UPDATE (data: UserStatus) {
      this.cartAssemblyPriceRegionalized = data.cartAssemblyPriceRegionalized;
      this.cartItemsCount = data.cartItemsCount;
      this.cartTotalPriceBeforeDiscountRegionalized = data.cartTotalPriceBeforeDiscountRegionalized;
      this.cartTotalPriceRegionalized = data.cartTotalPriceRegionalized;
      this.cartUsedAssembly = data.cart_used_assembly;
      this.cartUsedPromo = data.cartUsedPromo;
      this.promocodeValueRegionalized = `-${data.promocodeValueRegionalized}`;
      this.hasAssemblyPossible = data.hasAssemblyPossible;
      this.hasFastTrackPossible = data.is_valid_for_fast_track;
      this.cartUsedFastTrack = data.is_fast_track;
      this.hasT03 = data.hasT03;
      this.hasLighting = data.has_lighting;
      this.promocodeName = data.promoCodeName;
      this.shelfItemsCount = data.shelfItemsCount;
      this.cartItems = data.cartItems;
      this.waitingListExpireDate = data.waitingListExpireDate;
      this.T03Available = data.T03Available;
      this.orderPricing = data.order_pricing;
      this.deliveryTime = data.deliveryTime;
      this.isSKU = data.is_sku;
      this.totalPrice = data.order_pricing?.total_price;
      this.totalPriceBeforeDiscount = data.order_pricing?.total_price_before_discount;
      this.placedAt = data.placed_at?.length ? dateConverter(data.placed_at) : null;
      this.chosenPaymentMethod = data.chosen_payment_method;
    },
    LOAD_CART () {
      this.isCartLoaded = true;
    },
    OPEN_CART () {
      this.isCartOpen = true;
    },
    CLOSE_CART () {
      this.isCartOpen = false;
    }
  },
  getters: {
    ITEM_FOR_ASSEMBLY_COUNT: state => state.cartItems?.filter(item => item.itemFurnitureType !== 'sample_box' && item.shelf_type !== 3).length,
    ALL: state => state
  }
});
