import { useApi } from '~/composables/useApi';
import type { FormDataType } from '~/composables/checkout/handleFormValues';
import type { GlobalState } from '~/stores/global';

export const GET_FORM_DATA = (cartId: string | null) => useApi(`/api/v2/checkout/cart/${cartId}/initial_form_data/`);
export const GET_FORM_DATA_CONFIRMATION = (orderId: string | null) => useApi(`/api/v2/checkout/order/${orderId}/initial_form_data/`);

export const TRIGGER_CHECKOUT_ENTRY_EVENT = (global: GlobalState) => useApi(`api/v2/checkout/cart/${global.cartId}/trigger_checkout_entry_event/`, {
  method: 'post'
});
export const SET_FORM_DATA = (global: GlobalState, payload: FormDataType) => useApi(`/api/v2/checkout/cart/${global.cartId}/sync_with_order/`, {
  method: 'patch',
  body: {
    ...payload
  }
});
export const TRIGGER_CHECKOUT_FULFILLED_EVENT = (global: GlobalState) => useApi(`api/v2/checkout/cart/${global.cartId}/trigger_checkout_fulfilled_event/`, {
  method: 'post'
});
export const VAT_VALIDATION_COUNTRY = (global: GlobalState, invoiceCountry: string, vatNumber: string) => useApi<{validated:boolean}>(`/api/v2/checkout/cart/${global.cartId}/validate_vat_number/`, {
  method: 'post',
  body: {
    invoice_country: invoiceCountry,
    vat_number: vatNumber
  }
});
export const VALIDATE_FINAL_TRANSACTION_PRICE = (global: GlobalState) => useApi(`/api/v2/checkout/order/${global.orderId}/validate_order_before_payment/`, {
  method: 'post'
});

export const HANDLE_IS_FREE = (orderId: string | null) => useApi(`/api/v2/checkout/order/${orderId}/handle_free_order/`, {
  method: 'post'
});

export const CHANGE_STATUS_TO_PENDING = (global: GlobalState) => useApi(`/api/v1/order/${global.orderId}/change_status_to_pending/`, {
  method: 'post'
});

export const CHANGE_STATUS_TO_DRAFT = (global: GlobalState) => useApi(`/api/v1/order/${global.orderId}/change_status_to_draft/`, {
  method: 'post'
});
export const PAYMENTS_PRIMER = (global: GlobalState) => useApi(`/api/v1/payments/primer/?order_id=${global.orderId}`, {
  method: 'post'
});

export const CHOSEN_PAYMENT_METHOD = (global: GlobalState, ChosenPaymentMethod: string) => useApi(`/api/v1/order/${global.orderId}/update_payment_method/`, {
  method: 'patch',
  body: {
    chosen_payment_method: ChosenPaymentMethod
  }
});
export const PAYMENT_SESSION = (global: GlobalState) => useApi(`/api/v1/payment/session/?order_id=${global.orderId}`, {
  method: 'post'
});

export const ASSEMBLY = (id: string, assembly_enabled: boolean) => useApi(`/api/v2/cart/item/${id}/assembly/`, {
  method: 'patch',
  body: {
    assembly_enabled
  }
});
