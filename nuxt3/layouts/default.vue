<template>
  <main
    id="default-layout"
    v-bind="{
      style: `--ribbon-height:${ribbonHeight}px`,
      class: AB_TESTS_NAVIGATION_2025 ? '[--old-navbar-height:0px] [--navigation-modern-padding:88px] md:[--navigation-modern-padding:96px]'
        : '[--navigation-modern-padding:0px] [--old-navbar-height:56px] md:[--old-navbar-height:64px]'
    }"
  >
    <TheHeaderModern
      v-if="AB_TESTS_NAVIGATION_2025"
      v-bind:variant="themeVariant"
    />
    <TheHeader v-else />

    <NuxtPage class="modern-navigation-page-padding" />

    <slot name="content" />

    <template v-if="isNewsletterVisible">
      <NuxtLazyHydrate when-visible>
        <LazySectionNewsletter />
      </NuxtLazyHydrate>
    </template>

    <NuxtLazyHydrate when-visible>
      <LazyTheFooter2025 />
    </NuxtLazyHydrate>

    <ClientOnly>
      <TheGlobal />
    </ClientOnly>

    <ScartLazyLoader />

    <nav id="feed">
      <div class="content-card-background" />
    </nav>
  </main>
</template>

<script setup lang="ts">
const { AB_TESTS_NAVIGATION_2025 } = storeToRefs(useGlobal());
const i18nHead = useLocaleHead({ addSeoAttributes: true });
const { isNewsletterVisible } = useNewsletter();
const filteredHead = i18nHead.value.link.filter((item: { hreflang?: string }) => (item.hreflang && (!item.hreflang.includes('en-OT') && !item.hreflang.includes('en-UK') && !item.hreflang.includes('da-DA'))));

i18nHead.value.link = filteredHead;

useHead(i18nHead.value);

const route = useRoute();
const getRouteBaseName = useRouteBaseName();
const routeBaseName = computed(() => getRouteBaseName(route));
const { ribbonHeight } = storeToRefs(useHeaderStore());
const themeVariant = computed(() => {
  const routesWithLightTheme = ['homepage', 'sofa-teaser'];
  const routesWithTransparentTheme = ['homepage-new'];

  if (routesWithLightTheme.includes(routeBaseName.value)) {
    return 'light';
  } else if (routesWithTransparentTheme.includes(routeBaseName.value)) {
    return 'transparent';
  }

  return 'dark';
});

</script>

<style lang="scss">

.main-nav-bar ul {
    button,
    a {
        &.color-item {
            @apply text-[#FF3C00] hoverable:hover:text-[#FF3C00];
        }

        @apply hoverable:hover:text-offblack-900 leading-1 flex items-center p-12 md:px-[6px] xl:px-12;
        &.desktop-hidden {
            @apply lg:hidden;
        }

        &.mobile-hidden {
            @apply lg-max:hidden;
        }
    }
}

.navbar-item-counter {
    @apply top-8 right-8 md:top-8 md:right-2 xl:top-8 xl:right-8;
}

.navigation-reveal {
    &-enter-active,
    &-leave-active {
        @apply overflow-hidden transition-all ease-in-out duration-300;
    }

    &-enter-from,
    &-leave-to {
        @apply lg-max:h-0 #{!important};
        @apply lg:translate-y-[-100%];
    }
}

.navigation-drawer {
    @apply absolute top-0 w-full lg:top-full bg-white;
    @apply overflow-y-auto lg-max:h-[calc((100vh)-var(--ribbon-height))] lg:max-h-[calc((100vh)-var(--header-with-ribbon-height))];
}

.modern-navigation-page-padding {
    @apply pt-[var(--navigation-modern-padding)];
}

.reset-modern-navigation-page-padding {
    @apply pt-0 #{!important};
}

</style>
