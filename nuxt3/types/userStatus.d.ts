import type {ConfiguratorType} from "~/types/configurator";

export interface CartItem {
    itemUrl: string;
    translatedItemUrl: string;
    itemImageUrl: string;
    itemDescriptionMaterial: string;
    itemDescriptionDimensions: string;
    itemMaxLoad: number;
    id: number;
    itemId: number;
    shelfId: number;
    itemFurnitureType: FurnitureModel | 'sample_box';
    region_price: string;
    region_price_with_discount: number;
    region_price_in_euro: number;
    region_price_with_discount_in_euro: number;
    currency_code: string;
    pattern_name: string;
    category: string;
    translated_category_display: string | null;
    color_name: string | null;
    size_txt: string;
    width: number;
    height: number;
    density: string;
    depth: number;
    drawers: number;
    doors: number;
    shelf_type: SHELF_TYPE;
    material: string;
    lighting: boolean;
    digital_product_version: string;
    configuratorType: ConfiguratorType;
    physicalProductVersion: string;
    delivery_time: number;
    base_preset: string | null;
    promotion: number;
    itemPriceRegionalized: string;
    itemPriceWithoutDiscount: string;
    is_strikethrough_promo: boolean;
    item_price_regionalized_number: number;
    item_price_without_discount_regionalized_number: number;
    price_netto: number;
    price_without_discount: number;
    price_netto_without_discount: number;
    price_with_discount: number;
    omnibus_price: number;
    item_revenue_brutto: number;
    assemblyEnabled: boolean;
    assemblyRequired: boolean;
    assemblyPrice: string | null;
    regionAssemblyPrice: number | null;
    quantity: number;
    omnibusPrice: number | string | null;
    has_multiple_depths: boolean;
    has_multiple_heights: boolean;
    content_type: 'samplebox' | 'watty' | 'jetty' | 'sotty';
    name_key: string;
    color: number;
    status: string;
    variant_type: number;
    is_exterior: boolean;
    region_price: number;
    region_base_price: number;
}

export interface OrderPricing {
    total_price: number | null | undefined;
    total_price_before_discount: number | null | undefined;
    assembly_price: number;
    discount_value: number;
    recycle_tax_value: number;
    order_revenue_brutto: number;
    tax: number;
    order_total_price_netto: number;
    order_promo_amount_netto: number;
    vat_percentage_value: number;
    fast_track_price: number;
}

export interface UserStatus {
    is_fast_track: boolean;
    is_valid_for_fast_track: boolean;
    is_sku: boolean;
    is_valid_for_sku: boolean;
    region_name: string;
    cartItemsCount: number;
    cartTotalPriceRegionalized: string;
    cartTotalPriceBeforeDiscountRounded: number;
    cartTotalPriceBeforeDiscountRegionalized: string;
    cartAssemblyPrice: number;
    cartAssemblyPriceRegionalized: string;
    promocodeValueRegionalized: string;
    cartUsedPromo: boolean;
    cartUsedAssembly: boolean;
    promocodeName: string;
    hasAssemblyPossible: boolean;
    order_pricing: OrderPricing | null;
    furniture_price_brutto: number;
    payment_type: string | null;
    cartItems: CartItem[];
    has_lighting: boolean;
    library_items: number;
    signedIn: number;
    saleEnabled: boolean;
    shelfItemsCount: number;
    hasT03: boolean;
    hasT03Samples: boolean;
    cartRibbonEnabled: boolean;
    deliveryTime: string;
    T03Available: boolean;
    waitingListExpireDate: string | null;
    placed_at: string | null;
    chosen_payment_method: string | null;
}
