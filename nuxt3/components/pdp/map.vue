<template>
  <section
    class="aspect-[3/4] lg:aspect-[21/9]"
    data-testid="showrooms-map"
  >
    <ClientOnly>
      <GoogleMap
        style="width: 100%; height: 100%"
        v-bind="{
          apiKey: googleMapsApiKey,
          zoom: 7,
          maxZoom: 15,
          mapTypeControl: false,
          center,
          styles: [
            {
              featureType: 'all',
              stylers: [
                { saturation: -100 },
                { lightness: 18 }
              ],
            }
          ]
        }"
      >
        <CustomMarker
          v-for="showroom in showrooms as Showroom[]"
          v-bind:key="showroom.streetAddress"
          v-bind:options="{ position: { lat: Number(showroom.latitude), lng: Number(showroom.longitude) }, anchorPoint: 'BOTTOM_CENTER' }"
          v-on:click="showSelectedShowroom(showroom.name)"
        >
          <IconPin class="h-48" />
        </CustomMarker>
      </GoogleMap>

      <BaseDrawer
        ref="uspsDrawer"
        v-model="isDrawerOpen"
        v-bind="{
          modernDrawer: true,
          customCloseButtonClass: 'bg-neutral-900 text-white hover:bg-neutral-900 md-max:fixed md-max:top-64',
          additionalDialogPanelClasses: '!p-0 !w-auto flex items-stretch md:max-w-[90%]',
          slotContainerClass: 'min-w-full min-h-full'
        }"
      >
        <aside class="flex flex-col md:flex-row text-neutral-900 normal-16 h-full">
          <div class="xl:min-w-[570px] flex flex-col max-h-[100vh] justify-end">
            <img
              v-bind:src="selectedShowroom.displayImage"
              v-bind:alt="selectedShowroom.name"
              class="w-full h-full object-cover"
            >
            <div class="md:py-24 md:px-48 p-16">
              <h2
                class="semibold-24 md:semibold-28 xl:semibold-32"
                v-text="selectedShowroom.name"
              />
              <p
                class="mt-8"
                v-text="selectedShowroom.streetAddress"
              />
              <p v-text="`${selectedShowroom.postalCode} ${selectedShowroom.city}`" />
              <p
                class="mt-24"
                v-text="selectedShowroom.phone"
              />
              <a
                v-bind:href="`mailto:${selectedShowroom.email}`"
                v-text="selectedShowroom.email"
              />
              <br>
              <a
                v-bind:href="`https://www.google.com/maps?q=${selectedShowroom.latitude},${selectedShowroom.longitude}`"
                target="_blank"
                rel="noopener noreferrer"
                class="text-neutral-900 underline mt-24 inline-block"
              >
                {{ $t('pdp.showroom.get_directions') }}
              </a>
            </div>
          </div>
          <div class="xl:py-[72px] xl:px-48 md:py-56 md:px-32 p-16 bg-beige-100 md:min-w-[412px]">
            <h3
              class="semibold-20"
              v-text="$t('pdp.showroom.opening_hours')"
            />
            <ul class="mt-24">
              <li class="py-[6px] flex flex-row justify-between gap-x-4">
                <p>
                  {{ $t('pdp.showroom.monday') }}
                </p>
                <p>
                  <span v-if="selectedShowroom.monOpenTime && selectedShowroom.monCloseTime">
                    {{ selectedShowroom.monOpenTime }} - {{ selectedShowroom.monCloseTime }}
                  </span>
                  <span v-else>
                    {{ $t('pdp.showroom.closed') }}
                  </span>
                </p>
              </li>
              <li class="py-[6px] flex flex-row justify-between gap-x-4">
                <p>
                  {{ $t('pdp.showroom.tuesday') }}
                </p>
                <p>
                  <span v-if="selectedShowroom.tueOpenTime && selectedShowroom.tueCloseTime">
                    {{ selectedShowroom.tueOpenTime }} - {{ selectedShowroom.tueCloseTime }}
                  </span>
                  <span v-else>
                    {{ $t('pdp.showroom.closed') }}
                  </span>
                </p>
              </li>
              <li class="py-[6px] flex flex-row justify-between gap-x-4">
                <p>
                  {{ $t('pdp.showroom.wednesday') }}
                </p>
                <p>
                  <span v-if="selectedShowroom.wedOpenTime && selectedShowroom.wedCloseTime">
                    {{ selectedShowroom.wedOpenTime }} - {{ selectedShowroom.wedCloseTime }}
                  </span>
                  <span v-else>
                    {{ $t('pdp.showroom.closed') }}
                  </span>
                </p>
              </li>
              <li class="py-[6px] flex flex-row justify-between gap-x-4">
                <p>
                  {{ $t('pdp.showroom.thursday') }}
                </p>
                <p>
                  <span v-if="selectedShowroom.thuOpenTime && selectedShowroom.thuCloseTime">
                    {{ selectedShowroom.thuOpenTime }} - {{ selectedShowroom.thuCloseTime }}
                  </span>
                  <span v-else>
                    {{ $t('pdp.showroom.closed') }}
                  </span>
                </p>
              </li>
              <li class="py-[6px] flex flex-row justify-between gap-x-4">
                <p>
                  {{ $t('pdp.showroom.friday') }}
                </p>
                <p>
                  <span v-if="selectedShowroom.friOpenTime && selectedShowroom.friCloseTime">
                    {{ selectedShowroom.friOpenTime }} - {{ selectedShowroom.friCloseTime }}
                  </span>
                  <span v-else>
                    {{ $t('pdp.showroom.closed') }}
                  </span>
                </p>
              </li>
              <li class="py-[6px] flex flex-row justify-between gap-x-4">
                <p>
                  {{ $t('pdp.showroom.saturday') }}
                </p>
                <p>
                  <span v-if="selectedShowroom.satOpenTime && selectedShowroom.satCloseTime">
                    {{ selectedShowroom.satOpenTime }} - {{ selectedShowroom.satCloseTime }}
                  </span>
                  <span v-else>
                    {{ $t('pdp.showroom.closed') }}
                  </span>
                </p>
              </li>
              <li class="py-[6px] flex flex-row justify-between gap-x-4">
                <p>
                  {{ $t('pdp.showroom.sunday') }}
                </p>
                <p>
                  <span v-if="selectedShowroom.sunOpenTime && selectedShowroom.sunCloseTime">
                    {{ selectedShowroom.sunOpenTime }} - {{ selectedShowroom.sunCloseTime }}
                  </span>
                  <span v-else>
                    {{ $t('pdp.showroom.closed') }}
                  </span>
                </p>
              </li>
            </ul>
            <h3
              class="semibold-20 mt-48"
              v-text="$t('pdp.showroom.product_lines')"
            />
            <ul class="mt-24">
              <li
                v-for="item in selectedShowroom.showroomItems"
                v-bind:key="item.name"
                class="py-[8px] flex items-center flex-row gap-x-12"
              >
                <IconCheck
                  v-if="item.isVisible"
                  class="w-24 h-24 p-4"
                />
                <IconClose
                  v-else
                  class="w-24 h-24 opacity-50"
                />
                <p
                  v-bind:class="!item.isVisible && 'opacity-50'"
                  v-text="item.name"
                />
              </li>
            </ul>
          </div>
        </aside>
      </BaseDrawer>
    </ClientOnly>
  </section>
</template>

<script setup lang="ts">
import { GoogleMap, CustomMarker } from 'vue3-google-map';
import { GET_SHOWROOMS } from '~/api/pdp';

interface Showroom {
  name: string;
  link: string;
  displayImage: string;
  latitude: number;
  longitude: number;
  postalCode: string;
  city: string;
  streetAddress: string;
  phone: string;
  email: string;
  monOpenTime: string;
  monCloseTime: string;
  tueOpenTime: string;
  tueCloseTime: string;
  wedOpenTime: string;
  wedCloseTime: string;
  thuOpenTime: string;
  thuCloseTime: string;
  friOpenTime: string;
  friCloseTime: string;
  satOpenTime: string;
  satCloseTime: string;
  sunOpenTime: string;
  sunCloseTime: string;
  showroomItems: {
    name: string;
    isVisible: boolean;
  }[];
}

const center = { lat: 50, lng: 10 };
const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || 'AIzaSyAVNm578mndadq2PmZ8xc25qCEPFViQFg4';

const { data: showrooms } = await GET_SHOWROOMS();

const isDrawerOpen = ref(false);
const selectedShowroom = ref<Showroom | null>(null);

const showSelectedShowroom = (name: string) => {
  selectedShowroom.value = (showrooms.value as Showroom[]).find((showroom: Showroom) => showroom.name === name) || null;
  isDrawerOpen.value = true;
};
</script>
