<template>
  <ModalBasic
    v-model="model"
    modal-classes="!p-0 max-w-[560px] lg:max-w-[896px] rounded-[24px]"
    close-classes="!bg-neutral-100 rounded-full m-12 lg:m-16 z-2"
    v-on:after-close="$emit('close')"
  >
    <template #default>
      <div class="text-left text-neutral-900 lg-max:my-24 lg-max:mx-16 relative h-full grid lg:grid-cols-2 gap-16 lg:gap-0 items-center">
        <BasePicture
          class="w-full h-full aspect-square place-self-center"
          img-classes="object-cover w-full h-full aspect-square"
          type="A"
          data-testid="cart-item-image"
          v-bind="{
            alt: $t('scart.label.product_added'),
            path: selectedSamples.length > 1 ? 'pdp/sofa/samples/custom' : `lp/sample/sets/${selectedSamples[0]}`,
            pictureClasses: 'w-full',
            isRetinaUploaded: false
          }"
        />
        <div class="lg:px-32 lg:pt-96 lg:pb-32 place-self-center w-full">
          <section class="flex flex-col justify-center w-full">
            <h1 class="semibold-20 lg:semibold-24 lg:text-center">
              {{ $t('scart.label.product_added') }}
            </h1>
            <header class="flex w-full mt-16 lg:mt-32 gap-x-16 items-center justify-between  text-neutral-900">
              <div class="flex-1 self-start">
                <h2 class="semibold-16 line-clamp-2">
                  {{ $t('common.label.addToCartSamples') }}
                </h2>
              </div>
              <p
                class="price-regular-wrapper semibold-16"
                data-testid="item-price"
                v-html="priceSum"
              />
            </header>
            <p class="flex flex-wrap gap-4 normal-14 lg:normal-12 text-neutral-700 mt-12">
              {{ $t('scart.item.label.color') }}

              <span
                v-for="(item, index) in addedSamples"
                v-bind:key="index"
                class="text-pretty after:content-['/'] after:ml-4 after:last:hidden"
              >
                {{ $t(item.name_key) }}
              </span>
            </p>
            <p
              v-if="addedSamples.length > 1"
              class="flex flex-wrap gap-4 normal-14 lg:normal-12 text-neutral-700 mt-12"
            >
              {{ $t('common.label.quantity') }} {{ addedSamples.length }}
            </p>
            <div class="mt-32 lg:mt-48 space-y-16 w-full">
              <BaseLink
                variant="accent"
                class="w-full h-48"
                v-bind="{
                  trackData: { eventLabel: 'checkout' },
                  'data-testid': 'checkout',
                  href: isSignedIn ? `${$addLocaleToPath('checkout')}${cartId}/?uuid=${userId}`: $addLocaleToPath('register-or-login', { cart: 'true' }),
                }"
                v-on:click="$emit('close')"
              >
                {{ $t('scart.label.continue_to_checkout') }}
              </BaseLink>
              <BaseLink
                variant="outlined"
                class="w-full ty-btn h-48"
                v-bind="{
                  href: `${$addLocaleToURL('/cart')}${userId ? '?uuid='+userId : ''}`,
                  trackData: { eventLabel: 'view-cart' },
                  'data-testid': 'view-cart'
                }"
                v-on:click="$emit('close')"
              >
                {{ $t('scart.label.view_cart_2') }}
              </BaseLink>
            </div>
          </section>
        </div>
      </div>
    </template>
  </ModalBasic>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  priceSum: {
    type: String,
    required: true
  },
  selectedSamples: {
    type: Array,
    required: true
  }
});

const { isSignedIn, cartId, userId } = storeToRefs(useGlobal());

const addedSamples = computed(() => {
  return props.data.filter((sample: any) => props.selectedSamples.includes(sample.variant_type.toString()));
});

const model = defineModel<boolean>({ default: false });

defineEmits(['close']);
</script>
