<template>
  <div class="col-span-12 lg:col-span-6">
    <div class="lg:w-[95%] shadow-[0_4px_20px_rgba(0,0,0,0.08)] ">
      <div class="overflow-y-hidden max-h-[287px] lg2:max-h-[363px] xl:max-h-[401px] xl2:max-h-[459px]">
        <BasePicture
          disable-lazy
          class="bg-transparent"
          v-bind:type="materialObj.material_name === 'custom' ? 'A' : 'M T SD LD XLD'"
          v-bind="{
            path: `pdp/sofa/product-details/materials/${fabric}/${materialObj.material_name}`,
            alt: materialObj.alt,
          }"
        />
      </div>
      <div class="p-16 md:p-24">
        <div class="flex justify-between items-center">
          <div>
            <BasePicture
              picture-classes="overflow-hidden md:h-full max-w-[100px]"
              img-classes="w-full h-full object-cover object-bottom"
              type="A"
              disable-placeholder
              v-bind="{
                path: 'pdp/sofa/product-details/kvadrat',
                alt: 'kvadrat',
                isRetinaUploaded: false
              }"
            />
            <p class="mt-12 normal-16 text-neutral-900">
              {{ $t('Fabric') }}: {{ $t(`pdp_sofa_product_details_fabric_${fabric}`) }}
            </p>
          </div>
          <BaseButton
            variant="outlined"
            class="whitespace-nowrap"
            v-bind="{ trackData: {} }"
            v-on="{
              click: () => handleClick(),
            }"
          >
            <template #icon>
              <IconCart class="xl-max:hidden" />
            </template>
            <template #default>
              {{ $t('pdp_sofa_product_details_fabric_cta') }}
            </template>
          </BaseButton>
        </div>
        <div class="flex mt-12">
          <span
            v-for="(item, index) in configShelfType10"
            v-bind:key="index"
            class="rounded-full w-16 h-16 overflow-hidden mr-4"
            v-bind:class="{ 'border border-solid border-neutral-400': materialObj.value === item.value }"
          >
            <BasePicture
              disable-lazy
              class="bg-transparent"
              v-bind="{
                path: item.imgPath,
                alt: item.alt,
                type: 'RAW'
              }"
            />
          </span>
        </div>
      </div>
    </div>
  </div>
  <div class="col-span-12 lg:col-span-6">
    <TabGroup
      v-slot="{ selectedIndex }"
      as="div"
    >
      <TabList class="flex border-b border-solid border-neutral-400">
        <Tab
          v-for="(tabNameKey, index) in ['pdp_sofa_product_details_fabric_wool', 'pdp_sofa_product_details_fabric_corduroy'].filter(Boolean)"
          v-bind:key="index"
          class="relative pb-8 mr-32 last:mr-0
                   bold-14 text-neutral-700 normal-24 ui-selected:semibold-24 ui-selected:text-neutral-900 focus-visible:shadow-none cursor-pointer
                   transition-colors duration-300 ease-in-out
                   after:absolute after:left-0 after:bottom-[-1px] after:w-full after:h-[2px] after:bg-offblack-900
                   after:transform after:scale-x-0 after:origin-left
                   after:transition-transform after:duration-300 after:ease-in-out
                   ui-selected:after:!scale-x-100"
          data-testid="toggle-button"
        >
          {{ $t(tabNameKey) }}
        </Tab>
      </TabList>
      <div
        class="mt-16"
        v-bind:class="selectedIndex === 0 ? 'block' : 'hidden'"
      >
        <p
          class="normal-16 text-neutral-900 my-16 mb-32"
          v-html="$t('pdp_sofa_product_details_fabric_wool_body')"
        />
        <!--        <div class="lg:flex lg:justify-start lg:items-center mb-32">-->
        <!--          <div class="lg-max:mb-16 lg:mr-[100px] flex items-center justify-start">-->
        <!--            <IconGuaranteeNew />-->
        <!--            <p class="ml-12 text-neutral-900">-->
        <!--              {{ $t('Warranty 10 years') }}-->
        <!--            </p>-->
        <!--          </div>-->
        <!--          <div class="flex items-center justify-start">-->
        <!--            <IconGlobe />-->
        <!--            <p class="ml-12 text-neutral-900">-->
        <!--              {{ $t('Warranty 10 years') }}-->
        <!--            </p>-->
        <!--          </div>-->
        <!--        </div>-->
        <BaseDetails
          v-bind="{
            id: 'PDPprops.productDetailsInfo',
            title: $t('pdp_sofa_product_fabric_performance_headline'),
            labelClasses: '!text-neutral-900 semibold-20 lg:semibold-24',
          }"
          v-model="isOpenPerformance"
        >
          <p
            class="normal-16 text-neutral-900"
            v-html="$t('pdp_sofa_product_fabric_wool_performance_durability')"
          />
          <p
            class="normal-16 text-neutral-900 mt-8"
            v-html="$t('pdp_sofa_product_fabric_wool_performance_lightfastness')"
          />
          <p
            class="normal-16 text-neutral-900 mt-8"
            v-html="$t('pdp_sofa_product_fabric_wool_performance_pilling')"
          />
          <!--          <PdpSofaProductDetailsDetailList-->
          <!--            v-bind:list="performanceWool"-->
          <!--          />-->
        </BaseDetails>
        <BaseDetails
          v-bind="{
            id: 'PDPprops.productDetailsWarranty',
            title: $t('pdp_sofa_product_fabric_care_headline'),
            labelClasses: '!text-neutral-900 semibold-20 lg:semibold-24',
          }"
          v-model="isOpenCare"
        >
          <PdpSofaProductDetailsDetailList
            v-bind:list="careWool"
          />
          <!-- <div class="flex items-center justify-start mt-32">
            <IconPdf class="mr-12" />
            <BaseLink
              variant="underline-link"
              target="_blank"
              rel="noopener noreferrer"
              v-bind="{
                href: 'https://res.cloudinary.com/cstm/image/upload/crm/Tylko_Partners_Programme_Terms_and_Conditions.pdf',
                trackData: {}
              }"
            >
              {{ $t('Care instructions') }}
            </BaseLink>
          </div> -->
        </BaseDetails>
        <BaseDetails
          v-bind="{
            id: 'PDPprops.TestAndCertifications',
            title: $t('pdp_sofa_product_fabric_characteristics_headline'),
            labelClasses: '!text-neutral-900 semibold-20 lg:semibold-24',
          }"
          v-model="isOpenCharacteristics"
        >
          <ul class="fabric-list normal-16">
            <li>{{ $t('pdp_sofa_product_fabric_characteristics_composition') }}</li>
            <li>{{ $t('pdp_sofa_product_fabric_characteristics_certificates') }}</li>
            <li>{{ $t('pdp_sofa_product_fabric_characteristics_colour_differences') }}</li>
          </ul>
        </BaseDetails>
      </div>
      <div
        v-bind:class="selectedIndex === 1 ? 'block' : 'hidden'"
      >
        <p
          class="normal-16 text-neutral-900 my-16"
          v-html="$t('pdp_sofa_product_details_fabric_courdoroy_body')"
        />
        <!--        <div class="lg:flex lg:justify-start lg:items-center mb-32">-->
        <!--          <div class="lg-max:mb-16 lg:mr-[100px] flex items-center justify-start">-->
        <!--            <IconGuaranteeNew />-->
        <!--            <p class="ml-12 text-neutral-900">-->
        <!--              {{ $t('Warranty 10 years') }}-->
        <!--            </p>-->
        <!--          </div>-->
        <!--          <div class="flex items-center justify-start">-->
        <!--            <IconGlobe />-->
        <!--            <p class="ml-12 text-neutral-900">-->
        <!--              {{ $t('Warranty 10 years') }}-->
        <!--            </p>-->
        <!--          </div>-->
        <!--        </div>-->
        <BaseDetails
          v-bind="{
            id: 'PDPprops.productDetailsInfo',
            title: $t('pdp_sofa_product_fabric_performance_headline'),
            labelClasses: '!text-neutral-900 semibold-20 lg:semibold-24',
          }"
          v-model="isOpenPerformance"
        >
          <p
            class="normal-16 text-neutral-900"
            v-html="$t('pdp_sofa_product_fabric_courdoroy_performance_durability')"
          />
          <p
            class="normal-16 text-neutral-900 mt-8"
            v-html="$t('pdp_sofa_product_fabric_courdoroy_performance_lightfastness')"
          />
          <p
            class="normal-16 text-neutral-900 mt-8"
            v-html="$t('pdp_sofa_product_fabric_courdoroy_performance_pilling')"
          />
          <!--          <PdpSofaProductDetailsDetailList-->
          <!--            v-bind:list="performanceCorduroy"-->
          <!--          />-->
        </BaseDetails>
        <BaseDetails
          v-bind="{
            id: 'PDPprops.productDetailsWarranty',
            title: $t('pdp_sofa_product_fabric_care_headline'),
            labelClasses: '!text-neutral-900 semibold-20 lg:semibold-24',
          }"
          v-model="isOpenCare"
        >
          <PdpSofaProductDetailsDetailList
            v-bind:list="careCorduroy"
          />
          <!-- <div class="flex items-center justify-start mt-32">
            <IconDocument class="w-16 mr-12" />
            <BaseLink
              variant="underline-link"
              target="_blank"
              rel="noopener noreferrer"
              v-bind="{
                href: 'https://res.cloudinary.com/cstm/image/upload/crm/Tylko_Partners_Programme_Terms_and_Conditions.pdf',
                trackData: {}
              }"
            >
              {{ $t('Care instructions') }}
            </BaseLink>
          </div> -->
        </BaseDetails>
        <BaseDetails
          v-bind="{
            id: 'PDPprops.TestAndCertifications',
            title: $t('Characteristics'),
            labelClasses: '!text-neutral-900 semibold-20 lg:semibold-24',
          }"
          v-model="isOpenCharacteristics"
        >
          <ul class="fabric-list normal-16">
            <li>{{ $t('pdp_sofa_product_fabric_characteristics_composition_polyester') }}</li>
            <li>{{ $t('pdp_sofa_product_fabric_characteristics_certificates_oeko') }}</li>
            <li>{{ $t('pdp_sofa_product_fabric_characteristics_colour_differences') }}</li>
            <li>{{ $t('pdp_sofa_product_fabric_characteristics_shading_effect') }}</li>
          </ul>
        </BaseDetails>
      </div>
    </TabGroup>
  </div>
</template>

<script setup lang="ts">
import { Tab, TabGroup, TabList } from '@headlessui/vue';
import { configShelfType10 } from '~/consts/shelfType10';

const isOpenPerformance = ref(false);
const isOpenCare = ref(false);
const isOpenCharacteristics = ref(false);
const { width, depth, height, material, fabric } = storeToRefs(usePdpStore());

// const performanceWool = [
//   {
//     name: 'Durability',
//     value: '100,000 Martindale'
//   },
//   {
//     name: 'Pilling',
//     value: '4 (ISO 1-5)'
//   },
//   {
//     name: 'Lightfastnes',
//     value: '6-7 (ISO 1-8)'
//   },
//   {
//     name: 'Fire tests',
//     value: 'US Cal. Bull. 117-2013 • EN 1021-1/2 • NF D 60 013 • IMO FTP Code 2010 Part 8 • BS 5852 part 1 • AS/NZS 1530.3 • UNI 9175 1IM • ÖNORM B1/Q1 • AS/NZS 3837 class 2 • EN 13501 B-s2, d0 with treatment • SN 198 898 5.3 with treatment • ASTM E84 Class A Unadhered • BS 5852 crib 5 with treatment'
//   },
//   {
//     name: 'Fastness to rubbing',
//     value: '4-5 (Dry) 4-5 (Wet)'
//   },
//   {
//     name: 'Airflow',
//     value: '258 Pa s/m'
//   },
//   {
//     name: 'Absorption (100 mm)',
//     value: '0.50/Class D (Absorption flat)<br/>0.75/Class C (Absorption folded)'
//   },
//   {
//     name: 'Seam slippage',
//     value: '3.5 mm (warp), 5 mm (weft)'
//   },
//   {
//     additionalWrapperClasses: '!mt-32',
//     iconName: 'IconMockRectangle',
//     name: 'Marine use',
//     iconValue: 'IconMockRectangle',
//     value: 'Wet and dry crocking'
//   },
//   {
//
//     iconName: 'IconMockRectangle',
//     name: 'Flammability',
//     iconValue: 'IconMockRectangle',
//     value: 'Abrasion - high traffic'
//   },
//   {
//
//     iconName: 'IconMockRectangle',
//     name: 'Physical properties',
//     iconValue: 'IconMockRectangle',
//     value: 'EU Ecolabel'
//   },
//   {
//
//     iconName: 'IconMockRectangle',
//     name: 'Fire resistant',
//     iconValue: 'IconMockRectangle',
//     value: 'Colourfastness to light'
//   }
// ];

const careWool = [
  {
    iconName: 'IconDoNotWash',
    name: 'pdp_sofa_product_fabric_care_do_not_wash',
    iconValue: 'IconDoNotBleach',
    value: 'pdp_sofa_product_fabric_care_do_not_bleach'
  },
  {
    iconName: 'IconDoNotTumbleDry',
    name: 'pdp_sofa_product_fabric_care_do_not_tumble_dry',
    iconValue: 'IconIronLow',
    value: 'pdp_sofa_product_fabric_care_iron_low'
  },
  {
    iconName: 'IconDryCleaning',
    name: 'pdp_sofa_product_fabric_care_dry_cleaning'
  }
];

// const performanceCorduroy = [
//   {
//     name: 'Durability',
//     value: '100,000 Martindale'
//   },
//   {
//     name: 'Pilling',
//     value: '4 (ISO 1-5)'
//   },
//   {
//     name: 'Lightfastnes',
//     value: '6-7 (ISO 1-8)'
//   },
//   {
//     name: 'Fire tests',
//     value: 'US Cal. Bull. 117-2013 • EN 1021-1/2 • NF D 60 013 • IMO FTP Code 2010 Part 8 • BS 5852 part 1 • AS/NZS 1530.3 • UNI 9175 1IM • ÖNORM B1/Q1 • AS/NZS 3837 class 2 • EN 13501 B-s2, d0 with treatment • SN 198 898 5.3 with treatment • ASTM E84 Class A Unadhered • BS 5852 crib 5 with treatment'
//   },
//   {
//     name: 'Fastness to rubbing',
//     value: '4-5 (Dry) 4-5 (Wet)'
//   },
//   {
//     name: 'Airflow',
//     value: '258 Pa s/m'
//   },
//   {
//     name: 'Absorption (100 mm)',
//     value: '0.50/Class D (Absorption flat)<br/>0.75/Class C (Absorption folded)'
//   },
//   {
//     name: 'Seam slippage',
//     value: '3.5 mm (warp), 5 mm (weft)'
//   },
//   {
//     additionalWrapperClasses: '!mt-32',
//     iconName: 'IconMockRectangle',
//     name: 'Marine use',
//     iconValue: 'IconMockRectangle',
//     value: 'Wet and dry crocking'
//   },
//   {
//
//     iconName: 'IconMockRectangle',
//     name: 'Flammability',
//     iconValue: 'IconMockRectangle',
//     value: 'Abrasion - high traffic'
//   },
//   {
//
//     iconName: 'IconMockRectangle',
//     name: 'Physical properties',
//     iconValue: 'IconMockRectangle',
//     value: 'EU Ecolabel'
//   },
//   {
//
//     iconName: 'IconMockRectangle',
//     name: 'Fire resistant',
//     iconValue: 'IconMockRectangle',
//     value: 'Colourfastness to light'
//   }
// ];

const careCorduroy = [
  {
    iconName: 'IconWash40',
    name: 'pdp_sofa_product_fabric_care_wash_40',
    iconValue: 'IconDoNotBleach',
    value: 'pdp_sofa_product_fabric_care_do_not_bleach'
  },
  {
    iconName: 'IconDryCleaning',
    name: 'pdp_sofa_product_fabric_care_professional_dry_cleaning',
    iconValue: 'IconIronLow',
    value: 'pdp_sofa_product_fabric_care_iron_low'
  },
  {
    iconName: 'IconDoNotUseSteamWhenIron',
    name: 'pdp_sofa_product_fabric_care_do_not_use_steam'
  }
];

const materialObj = configShelfType10.filter(item => item.value === material.value)[0];

const handleClick = () => {
  if (window.PubSub) {
    window.PubSub.publish('openSamplesDrawer');
  }
};
</script>

<style lang="scss">
.fabric-list{
  @apply list-disc #{!important};
  @apply flex flex-col gap-8;
  @apply pl-16 list-disc list-outside text-neutral-900 text-balance;
}
</style>
