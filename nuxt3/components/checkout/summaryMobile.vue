<template>
  <Fragment>
    <CheckoutFormSection
      class="mt-12 !pb-24"
      data-testid="mobile-promo"
    >
      <h3 class="text-neutral-900 semibold-20 mb-24">
        {{ $t('checkout_discount.code_headline') }}
      </h3>
      <CheckoutPromocodeNew
        v-if="formDisplay"
        data-testid="checkout-mobile-promocode-new"
        v-bind="{
          eventCategory: 'checkout',
        }"
      />
    </CheckoutFormSection>

    <CheckoutFormSection
      class="mt-12 !pb-24"
    >
      <CheckoutSummaryDetailsNew
        v-if="formIsMounted"
        class="!pt-0"
        data-testid="checkout-mobile-summary"
        v-bind="{
          displayNewsletter: false
        }"
      />
    </CheckoutFormSection>
    <CheckoutFormSection
      v-if="displayNewsletter && (!sms || !newsletter)"
      class="mt-12 !pt-[1px] !pb-12"
      data-testid="checkout-mobile-sms-newsletter"
    >
      <CheckoutNewsletter
        v-if="formDisplay"
        v-bind="{
          shouldRegisterNewsletterToForm: true
        }"
      />
    </CheckoutFormSection>
    <CheckoutFormSection
      v-if="!isSignedIn && displayTerms"
      class="mt-12 !pt-[1px] !pb-12"
      data-testid="checkout-mobile-terms"
    >
      <CheckoutTermsNote />
    </CheckoutFormSection>
  </Fragment>
</template>

<script setup lang="ts">
defineProps({
  displayNewsletter: {
    type: Boolean,
    default: false
  },
  displayTerms: {
    type: Boolean,
    default: false
  }
});
const cart = useScartStore();
const { formDisplay } = useAdyenDropin();
const { formIsMounted, sms, newsletter } = storeToRefs(useCheckoutStore());
const { isSignedIn } = storeToRefs(useGlobal());
</script>
