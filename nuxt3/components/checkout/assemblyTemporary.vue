<template>
  <div
    v-if="cart.hasAssemblyPossible"
    class="group relative"
    v-bind:class="{ 'pending [&>.formkit-outer]:opacity-20 [&>.formkit-outer]:pointer-events-none': isPending }"
  >
    <FormKit
      v-model="cartUsedAssembly"
      type="tyBox"
      label-class="cursor-pointer normal-16 items-center"
      outer-class="w-full transition-opacity duration-100 ease-out"
      v-bind="{
        id,
        disabled: isPending,
      }"
    >
      <template #labelExtra>
        <div class="flex justify-between items-center">
          <div class="flex flex-col gap-2">
            <span class="text-neutral-900 semibold-16">{{ $t('scart.shelf_assembly_service') }}</span>
            <span class="text-neutral-700 normal-14">{{ $t('scart.shelf_assembly_and_delivery') }}</span>
          </div>
          <span
            class="pl-16 text-neutral-900 semibold-16"
            data-testid="assembly-price">
              {{ format(cart.orderPricing?.assembly_price) }}
          </span>
        </div>
      </template>
    </FormKit>
    <div
      v-if="isPending"
      class="pending-spinner absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
    />
  </div>
</template>

<script setup lang="ts">
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';

const cart = useScartStore();
const $gtm = useGtm();
const { checkout2025Event } = checkoutAnalytics();

const {
  cartUsedAssembly
} = storeToRefs(cart);

const id = useId();
const { handleAssembly } = useCart();
const { format } = usePrice();
const toBoolean = (value: string | boolean) => !!(value === 'true' || value === true);
const isPending = ref<boolean>(false);

watch(cartUsedAssembly, async (value, oldValue) => {
  // there is bug in formkit that it emits boolen value as string on initial which triggers this watch
  // so we need to check if value is actually changed
  const booleanValue = toBoolean(value);
  const booleanOldValue = toBoolean(oldValue);

  if (booleanValue !== booleanOldValue) {
    isPending.value = true;

    await handleAssembly(booleanValue);

    if (booleanValue) {
      $gtm.push({ ecommerce: null });
      $gtm.push(checkout2025Event('add_shipping_info', null, 'Home delivery'));
    }

    isPending.value = false;
  }
});
</script>
