<template>
  <div v-bind:class="containerClass">
    <div
      v-if="omnibusPrice"
      class="price-omnibus-wrapper text-right"
    >
      <div class="flex justify-end">
        <span
          class="whitespace-nowrap block"
          v-bind:class="[omnibusPriceClass, promoPriceFontClass]"
          data-testid="item-price"
          v-html="itemPriceRegionalized"
        />
        <span
          class="line-through block"
          v-bind:class="[omnibusStrikethroughPriceClass, priceFontClass]"
          v-html="priceWithoutDiscount"
        />
      </div>
    </div>
    <div
      v-else-if="!omnibusPrice && cartItem.promotion"
      class="price-promo-wrapper"
    >
      <p
        v-bind:class="[promoPriceClass, promoPriceFontClass]"
        class="whitespace-nowrap"
        data-testid="item-price"
        v-html="itemPriceRegionalized"
      />
      <span
        v-bind:class="[previousPriceClass, previousPriceFontClass]"
        class="line-through"
        v-html="priceWithoutDiscount"
      />
    </div>
    <p
      v-else
      class="price-regular-wrapper"
      v-bind:class="[regularPriceClass, priceFontClass]"
      data-testid="item-price"
      v-html="itemPriceRegionalized"
    />
    <p
      v-if="cartItem.quantity > 1"
      v-bind:class="[
        perItemPriceClass,
        lowestPriceFontClass,
        {
          'text-orange' : omnibusPrice
        }
      ]"
      data-testid="price-per-item"
    >
      <template v-if="cartItem.promotion">
        {{ $t('scart.item.per_item') }} {{ format( cartItem.region_price_with_discount, currencyCode, countryLocale) }}
      </template>
      <template v-else>
        {{ $t('scart.item.per_item') }} {{ format( cartItem.region_price, currencyCode, countryLocale) }}
      </template>
      <!--      checkout.lowest.price_info-->
    </p>
    <p
      v-if="omnibusPrice"
      v-bind:class="[omnibusLowestPriceClass, lowestPriceFontClass]"
    >
      <template v-if="!shortOmnibusPriceCopy">
        {{ $t('common.omnibusnotice') }} {{ format(omnibusPrice) }}
      </template>
      <template v-else>
        <template v-if="cartItem.quantity > 1">
          {{ $t('checkout.lowest.price_info') }} {{ format(omnibusPrice) }}
        </template>
        <template v-else>
          {{ $t('checkout.price_info') }} {{ format(omnibusPrice) }}
        </template>
      </template>
    </p>
  </div>
</template>

<script setup lang="ts">
import usePrice from '~/composables/usePrice';

import type { CartItem } from '~/types/userStatus';
const { format } = usePrice();
const props = withDefaults(defineProps<{
  cartItem: CartItem;
  regularPriceClass?: string;
  perItemPriceClass?: string;
  promoPriceClass?: string;
  previousPriceClass?: string;
  omnibusPriceClass?: string;
  omnibusStrikethroughPriceClass?: string;
  omnibusLowestPriceClass?: string;
  containerClass?: string;
  priceFontClass?: string;
  promoPriceFontClass?: string;
  previousPriceFontClass?: string;
  lowestPriceFontClass?: string;
  shortOmnibusPriceCopy?: boolean;
}>(), {
  regularPriceClass: 'text-neutral-750 text-right',
  previousPriceClass: 'text-neutral-700',
  perItemPriceClass: 'text-right text-neutral-700',
  promoPriceClass: 'semibold-14 text-orange mr-[0.3em]',
  omnibusPriceClass: 'text-orange mr-[0.6em]',
  omnibusStrikethroughPriceClass: '!text-neutral-700 self-center',
  omnibusLowestPriceClass: 'text-neutral-700 text-right',
  containerClass: '',

  priceFontClass: 'semibold-14',
  promoPriceFontClass: 'semibold-14',

  previousPriceFontClass: 'normal-14',
  lowestPriceFontClass: 'normal-12',
  shortOmnibusPriceCopy: true
});

const { currencyCode, countryLocale } = storeToRefs(useGlobal());

const omnibusPrice = computed(() => props.cartItem.omnibus_price ? props.cartItem.omnibus_price : false);

const priceWithoutDiscount = computed(() => format(props.cartItem.item_price_without_discount_regionalized_number, currencyCode.value, countryLocale.value));

const itemPriceRegionalized = computed(() => format(props.cartItem.item_price_regionalized_number, currencyCode.value, countryLocale.value));

</script>
