<template>
  <div class="flex flex-col">
    <!--  Partial price  -->
    <div
      class="cart-summary-section separator space-y-12 lg-max:!py-16"
      data-testid="cart-summary"
    >
      <ScartSimpleOption
        v-if="format(cart.totalPriceBeforeDiscount)"
        class="!py-0"
        data-testid="cart-summary-subtotal"
        v-bind="{
          title: $t('cart.subtotal'),
          value: format(cart.totalPriceBeforeDiscount),
          leftStyles: 'block normal-16 text-neutral-900',
          rightStyles: 'block normal-16 text-neutral-900'
        }"
      />
      <ScartSimpleOption
        class="!py-0"
        data-testid="cart-summary-delivery"
        v-bind="{
          title: $t('scart.delivery'),
          value: format(cart.orderPricing?.delivery_price),
          leftStyles: 'block normal-16 text-neutral-900',
          rightStyles: 'block normal-16 text-neutral-900'
        }"
      />
      <ScartSimpleOption
        v-if="cart.promocodeName"
        class="!py-0"
        data-testid="cart-summary-promocode"
        v-bind="{
          title: `${$t('checkout_discount.code_name')} ${cart.promocodeName}`,
          value: `-${format(cart.orderPricing?.discount_value)}`,
          leftStyles: 'block normal-16 text-neutral-900',
          rightStyles: 'block normal-16 text-neutral-900'
        }"
      />
      <ScartSimpleOption
        v-if="cart.hasT03"
        class="!py-0"
        data-testid="cart-summary-tone-assembly"
        v-bind="{
          title: $t('scart.wardrobe_assembly'),
          value: format(0),
          leftStyles: 'block normal-16 text-neutral-900',
          rightStyles: 'block normal-16 text-neutral-900'
        }"
      />
    </div>

    <!--  Promo code  -->
    <div class="cart-summary-section separator lg-max:-order-2 lg:!pt-0 lg:border-none">
      <h2 class="lg:hidden bold-20 text-neutral-900">
        {{ $t('checkout_discount.code_headline') }}
      </h2>
      <div class="mt-24 lg:my-0">
        <CheckoutPromocodeNew
          display-promo-code-remove-button
          disable-promocode-addition
          event-category="cart"
        />
      </div>
    </div>

    <!--  Assembly service  -->
    <div
      v-if="cart.hasAssemblyPossible"
      class="cart-summary-section separator lg-max:-order-1"
      data-testid="cart-summary-assembly"
    >
      <h2
        class="lg:hidden bold-20 text-neutral-900"
        data-testid="cart-summary-assembly-add"
      >
        {{ $t('scart.assembly_service.button.add') }}
      </h2>
      <CheckoutAssemblyTemporary class="mt-24 lg:my-0" />
    </div>

    <!--  Total price  -->
    <div class="cart-summary-section lg-max:pb-32">
      <div
        v-if="format(cart.totalPrice)"
        class="flex justify-between pt-16 border-t border-neutral-400 pb-4"
      >
        <h4
          class="semibold-20 text-neutral-900"
          v-html="$t('scart.total')"
        />
        <div class="flex items-end">
          <p
            class="semibold-20 text-neutral-900"
            data-testid="total-price"
            v-html="format(cart.totalPrice)"
          />
        </div>
      </div>
      <div class="text-right">
        <p
          v-if="global.regionCode !== 'FR'"
          class="normal-12 text-neutral-700 text-right md:mt-4"
          v-html="$t('checkout.form.vat_included', {
            vat: cart.orderPricing?.vat_percentage_value,
            netto: format(cart.orderPricing?.total_price_netto)
          })"
        />
        <p
          v-if="global.regionCode === 'FR'"
          class="normal-12 text-grey-900 inline-block"
          v-html="$t('scart.vat_included')"
        />
        <BaseButton
          v-if="global.regionCode === 'FR'"
          variant="custom"
          class="ml-4 normal-12 text-orange underline cursor-pointer inline-block"
          v-bind="{
            trackData: cartEvent('ShowEcoPartModal'),
            'data-testid': 'cart-ecopart-show'
          }"
          v-on:click="() => showRecycleTaxModal()"
        >
          <UiDotsLoader
            v-if="isPendingState"
            class="ml-8 text-orange w-56 !static !inline-flex gap-4 !translate-x-0 !translate-y-0"
            bounce-class="!w-8 !h-8 bg-orange"
          />

          <span
            v-else
            v-html="`${$t('scart.eco-fee')}`"
          />
        </BaseButton>
      </div>

      <BaseLink
        variant="accent"
        data-testid="cart-cta-button"
        v-bind="{
          asNuxtLink: !isSignedIn,
          href: isSignedIn ? `${$addLocaleToPath('checkout')}${cartId}/?uuid=${userId}`: $addLocaleToPath('register-or-login', { cart: 'true' }),
          trackData: cartEvent('GoToCheckout'),
        }"
        class="w-full mt-24 py-12"
      >
        {{ $t('scart.proceed_to_checkout') }}
      </BaseLink>
    </div>
    <ClientOnly>
      <ModalRecycleTax
        v-model="isRecycleTaxModalOpen"
        v-bind="{
          ...ecoTaxModalData
        }"
      />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { CART_ANALYTICS, handleRecycleTax } from '~/composables/useCart';
import usePrice from '~/composables/usePrice';
import { useScartStore } from '~/stores/scart';
import { useGlobal } from '~/stores/global';

const props = defineProps({
  displayPromoCode: {
    type: Boolean,
    default: true
  },
  displayAssemblyRemoveButton: {
    type: Boolean,
    default: true
  },
  eventCategory: {
    type: String,
    default: 'cart_edit'
  },
  displayNewsletter: {
    type: Boolean,
    default: false
  }
});

const cart = useScartStore();
const global = useGlobal();
const { format } = usePrice();
const { cartEvent } = CART_ANALYTICS(props.eventCategory);
const { isSignedIn, cartId, userId } = storeToRefs(useGlobal());

const {
  ecoTaxModalData,
  isRecycleTaxModalOpen,
  showRecycleTaxModal,
  isPendingState
} = handleRecycleTax();

</script>

<style>
.cart-summary-section {
  @apply lg-max:bg-white;
  @apply px-16 md:px-32 lg:px-0;
}
.cart-summary-section.separator {
  @apply py-24 mt-8;

  &:not(:first-child) {
    @apply lg:border-t lg:border-neutral-400;
  }
}
</style>
