<template>
  <main class="reset-modern-navigation-page-padding overflow-x-hidden">
    <div class="gallery-container bg-beige-200">
      <div class="col-span-2">
        <BaseCarousel
          class="sofa-gallery-carousel group lg:-mr-48"
          swiper-root-classes="h-full !overflow-visible"
          v-bind="{
            options: {
              pagination: {
                renderBullet: (index, className) => index === 2 ?
                  `<li class='swiper-pagination-bullet--video  ${className}'><svg fill='none' height='8' viewBox='0 0 6 8' width='6' xmlns='http://www.w3.org/2000/svg'><path d='m6 4-6.00000033 3.4641.00000031-6.928202z' fill='currentColor'/></svg></li>`
                  : `<li class='${className} !bg-white'></li>`
              },
              slidesPerView: 'auto',
              loop: true,
              breakpoints: 0,
              slidesPerGroup:1,
              loopAdditionalSlides:1,
              loopAddBlankSlides: false,
              navigation: {
                lockClass: 'hidden',
                disabledClass: 'swiper-button-off',
                nextEl: `#nextp-`,
                prevEl: `#prevp-`,
              },
            },
            name: `product_`,
            isNavigation: false,
            isPaginationDesktop: true,
            isPaginationMobile: true,
          }"
        >
          <template #pagination>
            <div
              class="hidden lg:block opacity-0 group-hover:opacity-100 transition-opacity basic-transition
                      absolute inset-0 z-1 pointer-events-none"
            >
              <button
                id="prevp-"
                disabled
                class="rounded-full bg-white p-12 pointer-events-auto shadow-icon
                        absolute top-1/2 left-24 -translate-y-1/2
                        [&:disabled]:opacity-40 transition-opacity basic-transition"
              >
                <IconCaretLeft class="w-24 h-24" />
              </button>
              <button
                id="nextp-"
                disabled
                class="rounded-full bg-white p-12 pointer-events-auto shadow-icon
                        absolute top-1/2 right-64 xl:right-80 -translate-y-1/2
                        [&:disabled]:opacity-40 transition-opacity basic-transition"
              >
                <IconCaretRight class="w-24 h-24" />
              </button>
            </div>
          </template>
          <template #default="{ activeIndex }">
            <BaseCarouselSlide
              v-for="(asset, index) in [
                { path: { ...pdpStore?.gallery, default: pdpStore.preview }, type: 'img' },
                { path: `${productMaterial?.galleryPaths.path}/${productMaterial?.galleryPaths.materialPath}/2/`, type: 'picture' },
                { path: `${productMaterial?.galleryPaths.path}/${productMaterial?.galleryPaths.materialPath}/3`, type: 'video' },
                { path: `${productMaterial?.galleryPaths.path}/${productMaterial?.galleryPaths.materialPath}/4/`, type: 'picture' },
                { path: `${productMaterial?.galleryPaths.path}/${productMaterial?.galleryPaths.materialPath}/5/${productMaterial?.galleryPaths.colorPath}`, type: 'picture' },
                { path: `${productMaterial?.galleryPaths.path}/${productMaterial?.galleryPaths.materialPath}/6/${productMaterial?.galleryPaths.colorPath}`, type: 'picture' },
              ]"
              v-bind:key="`sofa_${asset.path}`"
              class="gallery-slide-aspect"
            >
              <picture
                v-if="asset.type === 'img'"
                class="w-full gallery-slide-aspect object-cover"
              >
                <source
                  v-bind:srcset="asset.path?.mobile"
                  media="(max-width: 1023px)"
                  type="image/webp"
                >
                <source
                  v-bind:srcset="asset.path?.desktop"
                  media="(min-width: 1024px)"
                  type="image/webp"
                >
                <img
                  v-bind:src="asset.path?.default"
                  class="w-[calc(100%+2px)] h-[calc(100%+2px)] lg:w-[calc(100%+4px)] lg:h-[calc(100%+4px)] xl:w-[calc(100%+8px)] xl:h-[calc(100%+8px)]  object-cover"
                  alt=""
                >
              </picture>
              <BaseVideoCloudinary
                v-else-if="asset.type === 'video'"
                class="gallery-slide-aspect"
                type="M T SD LD XLD"
                v-bind="{
                  active: index === activeIndex,
                  path: asset.path,
                  posterPath: `${asset.path}/poster`,
                }"
              />
              <BasePicture
                v-else
                class="w-full gallery-slide-aspect object-cover"
                img-classes="w-full h-full object-cover"
                type="M T SD LD XLD"
                v-bind="{
                  isRetinaUploaded: false,
                  alt: '',
                  path: asset.path,
                }"
              />
            </BaseCarouselSlide>
          </template>
        </BaseCarousel>
      </div>
      <div class="relative z-2">
        <PdpSofaProductInfo
          v-bind:product-id="productId"
          v-bind:class="AB_TESTS_NAVIGATION_2025 ? 'lg:mt-120' : 'lg:mt-24'"
          class="lg:absolute z-2 lg:top-0 lg:right-0 lg:shadow-icon"
        />
      </div>
    </div>

    <NuxtLazyHydrate when-visible>
      <UiAnimatedHeadlineSection
        id="section-highlights"
        data-observe-view="highlights"
        class="relative pb-64 z-1 md:pb-80 xl:pb-144"
        data-section="section-highlights"
        v-bind="{
          headingCopy: $t('pdp.sofa.modular.title'),
          subheadingCopy: furnitureCategory === 'armchair' ? $t('pdp.sofa.modular.tagline.armchair') : $t('pdp.sofa.modular.tagline.default'),
          sectionWrapperClasses: 'bg-beige-200'
        }"
      >
        <LazyPdpVideoTilesCarousel
          class="grid-container"
          v-bind="{
            categoryName: 'sofa',
            shelfType: 10
          }"
        />
      </UiAnimatedHeadlineSection>
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <PdpSofaProductDetails
        v-bind="{
          productId
        }"
      />
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <LazySectionVideo
        id="hp-hero-video"
        data-observe-view="hero-video"
        data-section="hero-video"
        has-sound
        fill-viewport
        v-bind="{
          videoParams:{
            videoId: {
              mobile: 'tev53xevzc',
              desktop: 'z6tsm2vuif'
            },
            aspectRatio: {
              mobile: '1178/1768',
              desktop: '1920/960'
            }
          },
          ctaUrl: `${$addLocaleToPath('plp')}${$t('common.category.smooth.all.plp_url')}/`,
          ctaCopy: $t('pdp.sofa.smooth.cta'),
          title: $t('pdp.sofa.smooth.title'),
          description: $t('pdp.sofa.smooth.description'),
          videoPlaceHolder: 'homepage/video-placeholders/sofs_lunch',
        }"
      />
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <LazyPdpPlusPopups />
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <LazyPdpSofaSamples />
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <section>
        <div class="py-48 md:py-64 xl:py-96 bg-beige-200">
          <div class="grid-container">
            <p class="uppercase semibold-12 text-neutral-750 mb-8">
              {{ $t('pdp.sofa.highlights.tagline') }}
            </p>
            <h3 class="semibold-28 lg:semibold-44 xl:semibold-54 text-neutral-900 mb-32 lg:mb-48 max-w-[635px]">
              {{ $t('pdp.sofa.highlights.title') }}
            </h3>
          </div>

          <SectionFourTiles>
            <template #tile1>
              <CardDesign
                v-bind="{
                  imgPath: 'pdp/sofa/longevity/1',
                  imgAlt: 'lorem',
                  imgType: 'M T SD LD XLD',
                  index: 1,
                  heading: $t('pdp.sofa.highlights.carousel.item1.title'),
                  copy: $t('pdp.sofa.highlights.carousel.item1.description')
                }"
              />
            </template>
            <template #tile2>
              <CardDesign
                v-bind="{
                  imgPath: 'pdp/sofa/longevity/2',
                  imgAlt: 'lorem',
                  imgType: 'M T SD LD XLD',
                  index: 2,
                  heading: $t('pdp.sofa.highlights.carousel.item2.title'),
                  copy: $t('pdp.sofa.highlights.carousel.item2.description')
                }"
              />
            </template>
            <template #tile3>
              <CardDesign
                v-bind="{
                  imgPath: 'https://media.tylko.com/cloudinary/pdp/sofa/longevity/gif',
                  gif: true,
                  imgAlt: 'lorem',
                  imgType: 'M T SD LD XLD',
                  index: 3,
                  heading: $t('pdp.sofa.highlights.carousel.item3.title'),
                  copy: $t('pdp.sofa.highlights.carousel.item3.description')
                }"
              />
            </template>
            <template #tile4>
              <CardDesign
                v-bind="{
                  imgPath: 'pdp/sofa/longevity/4',
                  imgAlt: 'lorem',
                  imgType: 'M T SD LD XLD',
                  index: 4,
                  heading: $t('pdp.sofa.highlights.carousel.item4.title'),
                  copy: $t('pdp.sofa.highlights.carousel.item4.description')
                }"
              />
            </template>
          </SectionFourTiles>
        </div>
      </section>
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <LazyPdpReviews
        data-observe-view="Reviews"
        extra-classes="lg-max:pt-32"
        v-bind="{
          reviews: reviews || [],
          reviewsCount: reviewsCount || 0,
          reviewsAverageScore: reviewsAverageScore || 0
        }"
      />
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <LazySectionCreators
        id="pdp-creators"
        data-observe-view="creators"
        data-section="creators"
        v-bind="{
          tagline: $t('pdp.sofa.creators.tagline'),
          headline: $t('pdp.sofa.creators.headline'),
          buttonHref: $addLocaleToPath('lp.influencers'),
          carouselName: 'creators',
          items: creators
        }"
      />
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <div class="py-48 md:py-64 xl:py-96 bg-beige-200">
        <div class="grid-container">
          <p class="uppercase semibold-12 text-neutral-750 mb-8">
            {{ $t('pdp.sofa.minigrid.tagline') }}
          </p>
          <div class="md:flex justify-between items-start">
            <h3 class="semibold-28 lg:semibold-44 xl:semibold-54 text-neutral-900 max-w-[635px]">
              {{ $t('pdp.sofa.minigrid.title') }}
            </h3>
            <BaseLink
              v-bind="{
                href: $addLocaleToPath('plp'),
                variant: 'outlined',
                trackData: { eventLabel: 'cta', eventPath: $addLocaleToPath('plp') }
              }"
              class="md-max:mt-16"
            >
              {{ $t('common.view_all') }}
            </BaseLink>
          </div>
        </div>
        <LazySectionMinigrid
          data-observe-view="hp-minigrid"
          data-section="hp-minigrid"
          class="!pt-32"
          v-bind="{
            id: 'hp_minigrid-browse-by-room',
            itemListName: 'mini_grid',
            boardId: 'hp_rooms_living-room',
            additionalClasses: '!mt-0',
            ctaLink: $addLocaleToPath('plp'),
          }"
        />
      </div>
    </NuxtLazyHydrate>

    <NuxtLazyHydrate when-visible>
      <LazyPdpMap />
    </NuxtLazyHydrate>
  </main>
</template>

<script lang="ts" setup>
import { useTrackSectionView } from '~/composables/useTracking';
import { INITIAL_SOFA } from '~/api/pdp';
import { getSottyMaterialById } from '~/consts/shelfType10';

const route = useRoute();
const pdpStore = usePdpStore();
const config = useRuntimeConfig();

const { locale } = useLocale();
const { $logException, $addLocaleToPath } = useNuxtApp();

const { AB_TESTS_NAVIGATION_2025 } = storeToRefs(useGlobal());
const { reviews, reviewsCount, reviewsAverageScore, furnitureCategory } = storeToRefs(pdpStore);
const { furniture } = route.params;
const [productId] = furniture.split('?').join(',').split(',');
const { data, error }: { data: any, error: any } = await INITIAL_SOFA(productId, locale.value, `${config.public.baseURL}${route.path}`);

if (error.value) {
  $logException(error.value);
} else {
  pdpStore.SET_PDP_DATA(data.value);
}

const productMaterial = getSottyMaterialById(pdpStore.material!);

const creators = [
  {
    tagline: 'hp.creators.title1',
    title: 'hp.creators.name1',
    description: 'hp.creators.description1',
    picturePath: 'homepage/creators/1',
    href: $addLocaleToPath('lp.influencers') + '?influencer=Romain+Orchids',
    trackLabel: 'Creators 1',
    productLine: 'common.product_line.original_classic'
  },
  {
    tagline: 'hp.creators.title2',
    title: 'hp.creators.name2',
    description: 'hp.creators.description2',
    picturePath: 'homepage/creators/2',
    href: $addLocaleToPath('lp.influencers') + '?influencer=Romain+Orchids',
    trackLabel: 'Creators 2',
    productLine: 'common.product_line.original_modern'
  },
  {
    tagline: 'hp.creators.title3',
    title: 'hp.creators.name3',
    description: 'hp.creators.description3',
    picturePath: 'homepage/creators/3',
    href: $addLocaleToPath('lp.influencers') + '?influencer=Romain+Orchids',
    trackLabel: 'Creators 3',
    productLine: 'common.product_line.edge'
  },
  {
    tagline: 'hp.creators.title4',
    title: 'hp.creators.name4',
    description: 'hp.creators.description4',
    picturePath: 'homepage/creators/4',
    href: $addLocaleToPath('lp.influencers') + '?influencer=Romain+Orchids',
    trackLabel: 'Creators 4',
    productLine: 'common.product_line.tone'
  },
  {
    tagline: 'hp.creators.title5',
    title: 'hp.creators.name5',
    description: 'hp.creators.description5',
    picturePath: 'homepage/creators/5',
    href: $addLocaleToPath('lp.influencers') + '?influencer=Romain+Orchids',
    trackLabel: 'Creators 5',
    productLine: 'common.product_line.edge'
  },
  {
    tagline: 'hp.creators.title6',
    title: 'hp.creators.name6',
    description: 'hp.creators.description6',
    picturePath: 'homepage/creators/6',
    href: $addLocaleToPath('lp.influencers') + '?influencer=Romain+Orchids',
    trackLabel: 'Creators 6',
    productLine: 'common.product_line.edge'
  },
  {
    tagline: 'hp.creators.title7',
    title: 'hp.creators.name7',
    description: 'hp.creators.description7',
    picturePath: 'homepage/creators/7',
    href: $addLocaleToPath('lp.influencers') + '?influencer=Romain+Orchids',
    trackLabel: 'Creators 7',
    productLine: 'common.product_line.edge'
  }
];

useTrackSectionView('pdp_section_view', 'view');

definePageMeta({
  key: () => 'pdp',
  middleware: ['auth', 'pdp-middleware'],
  tag: ['pdp', 'pdp_smooth']
});

useHead(() => ({
  script: [
    { hid: 'inline-fb-product' },
    {
      type: 'text/javascript',
      innerHTML: `window.fb_content_type = "product";window.content_ids = ["${productId}"];`
    }
  ]
}));
</script>

<style lang="scss">
.gallery-slide-aspect{
  @apply aspect-[393/422] md:aspect-[768/825] lg:aspect-[837/628] xl:aspect-[939/704] xl2:aspect-[1115/836]
}

.gallery-container {
  @apply block lg:grid;
  @apply lg:grid-cols-[48px_57fr_43fr_48px];
  @apply xl:grid-cols-[56px_62fr_38fr_56px];

  @media only screen and (min-width: 1632px) {
    @apply xl:grid-cols-[1fr_942px_578px_1fr];
  }

}
.base-carousel.sofa-gallery-carousel {
  .swiper-pagination {
    @apply bg-beige-100/80 shadow-icon;
    @apply flex items-center gap-8 p-8 rounded-full;
    @apply absolute bottom-16 lg:bottom-16 left-1/2 transform -translate-x-1/2 #{!important};

    &-bullet {
      @apply bg-white #{!important};
      @apply w-8 h-8 rounded-full border border-neutral-900  cursor-pointer;
    }

    &-bullet--video {
      @apply flex items-center justify-center pl-2;
      @apply w-16 h-16 #{!important};

      &.swiper-pagination-bullet-active{
        @apply text-white;
      }
    }

    &-bullet-active {
      @apply bg-neutral-900 #{!important};
    }
  }
}
</style>
