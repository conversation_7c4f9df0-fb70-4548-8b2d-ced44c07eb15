import { GET_SHELF_TYPE } from '~/utils/types';
import { useAnalytics } from '~/composables/analytics/useAnalytics';
import useColors from '~/composables/useColors';
import { useScartStore } from '~/stores/scart';
import { dnaName } from '~/helpers/getDnaName';

export const pdpAnalytics = (dataState: any) => {
  const { userId, userHashEmail: userEmail } = storeToRefs(useGlobal());
  const { cartItems, promocodeName, cartUsedAssembly } = storeToRefs(useScartStore());
  const { getColor } = useColors();
  const { getFurnitureEnglishName: getItemName } = useAnalytics();
  const { $logException } = useNuxtApp();
  const { FETCH_GLOBAL, REGION_NAME } = useGlobal();

  const viewItemGA4 = (productID: any) => {
    const { materialName } = getColor(dataState.shelfType, dataState.material);

    return {
      event: 'view_item',
      userId: userId.value,
      ecommerce: {
        currency: 'EUR',
        value: parseFloat(dataState.priceInEuro).toFixed(2),
        value_f: parseFloat(dataState.priceInEuro).toFixed(2),
        product_type: 'furniture',
        items: [{
          item_id: productID,
          item_name: getItemName(
            dataState.shelfType <= 2 ? 'jetty' : dataState.shelfType === 10 ? 'sotty' : 'watty',
            dataState.patternName,
            dataState.furnitureCategory,
            dataState.shelfType
          ),
          affiliation: 'Tylko',
          quantity: 1,
          promotion_id: null, // TODO: BE,
          coupon: null, // TODO: Front
          discount: 0, // TODO: BE
          index: null, // TODO: BE
          item_brand: 'furniture',
          item_category: dataState.furnitureCategory,
          item_category2: dataState.patternName,
          item_category3: dataState.configuratorType,
          item_category4: null, // TODO: BE
          item_category5: materialName,
          item_list_name: null, // TODO: BE
          item_list_id: null, // TODO: BE
          item_variant: GET_SHELF_TYPE(dataState.shelfType, true),
          price: parseFloat(dataState.priceInEuro).toFixed(2),
          price_discounted: parseFloat(dataState.priceWithDiscountInEuro).toFixed(2),
          promotion_name: null, // TODO: BE,
          assembly: dataState.shelfType === 3 ? 'built-in' : 'additional', // TODO: Nie ma dostepu do carta. Do potwierdzenia czy tak to ma wyglądać
          item_id_preset: productID,
          discount_rate: null // TODO front
        }]
      }
    };
  };

  const a2cGa4Event = async (productID: any) => {
    if (!userId.value) {
      await FETCH_GLOBAL();
    }

    if (!cartItems.value) {
      $logException('a2cGa4Event: cart is empty');
      return null;
    }

    const item = cartItems.value[0];
    const payloadPrice = parseFloat(item?.region_price_in_euro).toFixed(2);
    const { materialName } = getColor(item.shelf_type, item.material);
    const properCategoryNameList: any = {
      'vinyl storage': 'vinyl_storage',
      'bedside table': 'bedside_table'
    };

    const properCategoryName = item.category === 'vinyl storage' || item.category === 'bedside table' ? properCategoryNameList[item.category] : item.category;

    return {
      event: 'add_to_cart',
      userId: userId.value,
      ecommerce: {
        currency: 'EUR',
        value: payloadPrice,
        value_f: payloadPrice,
        value_netto: parseFloat(item.price_netto).toFixed(2),
        product_type: 'furniture',
        coupon: promocodeName?.value ? promocodeName?.value : promocodeName,
        items: [{
          item_id: item.itemId,
          item_name: getItemName(
            item.shelf_type <= 2 ? 'jetty' : item.shelf_type === 10 ? 'sotty' : 'watty',
            item.pattern_name,
            item.category,
            item.shelf_type
          ),
          affiliation: 'Tylko',
          quantity: 1,
          promotion_id: null, // TODO: BE,
          coupon: null, // TODO: BE
          discount: null, // TODO: BE
          index: null, // TODO: BE
          item_brand: 'furniture',
          item_category: properCategoryName,
          item_category2: item.pattern_name,
          item_category3: item.configuratorType,
          item_category4: null, // TODO: BE
          item_category5: materialName,
          item_list_name: null, // TODO: BE
          item_list_id: null, // TODO: BE
          item_variant: GET_SHELF_TYPE(item.shelf_type, true),
          price: payloadPrice,
          price_discounted: parseFloat(item?.region_price_with_discount_in_euro).toFixed(2),
          promotion_name: null, // TODO: BE
          assembly: item.shelf_type === 3 ? 'built-in' : (cartUsedAssembly.value ? 'additional' : null), // TODO: BE
          item_id_preset: productID, // TODO: BE,
          discount_rate: null // TODO BE
        }]
      }
    };
  };

  const cartGenerateLead = (hashEmail: string) => ({
    event: 'generate_lead',
    userId: userId.value,
    placement: 'save_for_later_cart',
    leadsUserData: {
      ea: hashEmail,
      address: {
        country: REGION_NAME
      }
    }
  });

  const s4lGa4Event = async (item: any, productID: any, email?: any) => {
    const { materialName } = getColor(item.shelf_type, item.material);
    const payloadPrice = parseFloat(item?.region_price_in_euro).toFixed(2);
    const properCategoryNameList: any = {
      'vinyl storage': 'vinyl_storage',
      'bedside table': 'bedside_table'
    };
    const properCategoryName = item.category === 'vinyl storage' || item.category === 'bedside table' ? properCategoryNameList[item.category] : item.category;

    if (!userId.value) {
      await FETCH_GLOBAL();
    }

    return {
      event: 'add_to_wishlist',
      eventLabel: undefined,
      userId: userId.value,
      ea: email || userEmail,
      ecommerce: {
        currency: 'EUR',
        value: payloadPrice,
        value_f: payloadPrice,
        value_netto: null, // TODO: BE,
        product_type: 'furniture',
        coupon: null, // TODO: BE,
        items: [{
          item_id: item.shelfId,
          item_name: getItemName(
            item.shelf_type <= 2 ? 'jetty' : item.shelf_type === 10 ? 'sotty' : 'watty',
            item.pattern_name,
            item.category,
            item.shelf_type
          ),
          affiliation: 'Tylko',
          quantity: 1,
          promotion_id: null, // TODO: BE,
          coupon: null, // TODO: BE
          discount: null, // TODO: BE
          index: null, // TODO: BE
          item_brand: 'furniture',
          item_category: properCategoryName,
          item_category2: item.pattern_name ? item.pattern_name : dnaName[item.pattern],
          item_category3: item.configuratorType ? item.configuratorType : item.configurator_type,
          item_category4: null, // TODO: BE
          item_category5: materialName,
          item_list_name: null, // TODO: BE
          item_list_id: null, // TODO: BE
          item_variant: GET_SHELF_TYPE(item.shelf_type, true),
          price: payloadPrice,
          price_discounted: parseFloat(item?.region_price_with_discount_in_euro).toFixed(2),
          promotion_name: null, // TODO: BE
          assembly: item.shelf_type === 3 ? 'built-in' : 'additional',
          item_id_preset: item.base_preset,
          discount_rate: null // TODO BE
        }]
      }
    };
  };

  return {
    viewItemGA4,
    a2cGa4Event,
    s4lGa4Event,
    cartGenerateLead
  };
};
