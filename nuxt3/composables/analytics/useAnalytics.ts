import { get } from 'lodash-es';
import englishTypesTranslations from '../../locales/types/en.json';
import { TYPES } from '~/consts/types';
import useSofaCartInfo from '~/composables/useSofaCartInfo';

const types: any = TYPES();

const getFurnitureEnglishName = (itemFurnitureType: string, patternName: string, category: string, shelfType: string, contentType: string) => {
  if (itemFurnitureType === 'sample_box') {
    return `sample_${patternName}`;
  }

  if (contentType === 'sotty') {
    const {
      sofaItemName
    } = useSofaCartInfo();
    return sofaItemName;
  }

  const item: any = Object.values(types[itemFurnitureType]).find((type: any) => type.shelfType === shelfType);
  const typeKey = item.nameKey;
  const categoryKey = item.categories[category]?.nameKey;

  return `${get(englishTypesTranslations, typeKey)}${get(englishTypesTranslations, categoryKey)}`;
};

export const useAnalytics = () => {
  return {
    getFurnitureEnglishName
  };
};
