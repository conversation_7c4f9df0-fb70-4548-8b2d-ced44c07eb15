import { AiFillDollarCircle } from "react-icons/ai";

export default {
  name: "seasonSale",
  title: "seasonSale",
  type: "document",
  icon: AiFillDollarCircle,
  groups: [
    {
      name: "colors",
      title: "Colors",
      default: true,
    },
  ],
  fields: [
    {
      name: "title",
      title: "Title",
      type: "string",
    },
    {
      name: "seasonSaleColors",
      title: "Black Friday / Cyber Monday",
      type: "object",
      group: "colors",
      fields: [
        {
          name: "seasonSaleOn",
          type: "boolean",
          title: "Season SALE on?",
          initialValue: false,
        },
        {
          name: 'primaryColor',
          type: 'string',
          title: 'Primary Color'
        },
        {
          name: 'secondaryColor',
          type: 'string',
          title: 'Secondary Color'
        },
        {
          name: 'backgroundColor',
          type: 'string',
          title: 'Background Color'
        },
        {
          name: 'pdpBadgeLabel',
          type: 'string',
          title: 'PDP Badge Label'
        },
        {
          name: 'plpBadgeLabel',
          type: 'string',
          title: 'PLP Badge Label'
        },
        {
          name: 'plpBannerLabel',
          type: 'string',
          title: 'PLP Banner Label'
        },
        {
          name: 'promoCode',
          type: 'string',
          title: 'Promo Code'
        }
      ],
    },
  ],
};
