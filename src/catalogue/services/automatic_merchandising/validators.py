from typing import (
    List,
    Optional,
)

from catalogue.enums import StrategyEnum
from regions.models import Region


def validate_edge_only_option_availability(
    strategy: StrategyEnum,
    regions: List[Optional[str]] | Region | None,
) -> None:
    if strategy != StrategyEnum.PROFIT_NETTO:
        raise ValueError(
            '"Edge wardrobes only" option is available only for "Profit-netto" '
            'strategy.'
        )
    if regions:
        raise ValueError(
            '"Edge wardrobes only" option is not available for main markets.'
        )
