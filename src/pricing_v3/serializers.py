from decimal import Decimal

from rest_framework import serializers

from carts.models import Cart
from custom.enums import ShelfType
from custom.fields import CurrencyNumberField
from orders.models import Order
from orders.services.vat_details import (
    STANDARD_VAT,
    VatDetailsGetter,
)


class OmnibusPriceSerializer(serializers.Serializer):
    shelf_type = serializers.ChoiceField(choices=ShelfType.choices())
    geometry = serializers.JSONField()


class PricingSerializer(serializers.Serializer):
    total_price = CurrencyNumberField(
        source='get_total_price_number',
        initial=0,
        default=0,
    )
    total_price_netto = CurrencyNumberField(
        source='get_total_value_net',
        initial=0,
        default=0,
    )
    total_price_before_discount = CurrencyNumberField(
        source='get_total_price_number_before_discount',
        initial=0,
        default=0,
    )
    assembly_price = CurrencyNumberField(
        source='get_assembly_price',
        initial=0,
        default=0,
    )
    assembly_price_in_euro = CurrencyNumberField(
        source='get_assembly_price_in_euro',
        initial=0,
        default=0,
    )
    delivery_price = CurrencyNumberField(
        source='get_delivery_price',
        initial=0,
        default=0,
    )
    delivery_price_in_euro = CurrencyNumberField(
        source='get_delivery_price_in_euro',
        initial=0,
        default=0,
    )
    discount_value = CurrencyNumberField(
        source='region_promo_amount',
        initial=0,
        default=0,
    )
    recycle_tax_value = CurrencyNumberField(
        source='get_recycle_tax_value',
        initial=0,
        default=0,
    )
    order_revenue_brutto = CurrencyNumberField(
        source='get_base_total_value',
        initial=0,
        default=0,
    )
    tax = CurrencyNumberField(
        source='get_base_vat_value',
        initial=0,
        default=0,
    )
    order_total_price_netto = CurrencyNumberField(
        source='get_base_total_value_net',
        initial=0,
        default=0,
    )
    order_promo_amount_netto = CurrencyNumberField(
        source='get_base_promo_amount_net',
        initial=0,
        default=0,
    )
    vat_percentage_value = serializers.SerializerMethodField()

    @staticmethod
    def get_vat_percentage_value(instance: Cart | Order) -> Decimal:
        if not instance:
            return STANDARD_VAT * Decimal('100')
        vat_details = VatDetailsGetter(instance)
        as_percent = vat_details.get_vat_rate_for_display() * Decimal('100')
        if as_percent == int(as_percent):
            return Decimal(as_percent).quantize(Decimal('1'))
        return Decimal(as_percent).quantize(Decimal('.1'))


class CartPricingSerializer(PricingSerializer):
    delivery_price = serializers.SerializerMethodField()
    delivery_promo_price = serializers.SerializerMethodField()
    delivery_promo = serializers.SerializerMethodField()

    assembly_price = serializers.SerializerMethodField()
    assembly_promo_price = serializers.SerializerMethodField()
    assembly_promo = serializers.SerializerMethodField()

    old_sofa_collection_price = CurrencyNumberField(
        source='get_old_sofa_collection_price',
        initial=0,
        default=0,
    )

    def _get_delivery_price_without_promo(self, obj: Cart) -> Decimal:
        return obj.get_delivery_price_without_promo()

    def _get_delivery_price_with_promo(self, obj: Cart) -> int:
        return obj.get_delivery_price()

    def _get_assembly_price(self, obj: Cart) -> int:
        return obj.get_assembly_price()

    def _has_delivery_discount(self, obj: Cart) -> bool:
        if not obj.used_promo or not obj.has_s01:
            return False
        return obj.used_promo.delivery_discounts.exists()

    def get_delivery_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        return self._get_delivery_price_without_promo(obj)

    def get_delivery_promo(self, obj: Cart) -> bool:
        if not obj:
            return False
        return self._has_delivery_discount(obj)

    def get_delivery_promo_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        return Decimal(self._get_delivery_price_with_promo(obj))

    def get_assembly_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        assembly_price = self._get_assembly_price(obj)
        delivery_price = self._get_delivery_price_without_promo(obj)
        return Decimal(assembly_price + delivery_price)

    def get_assembly_promo(self, obj: Cart) -> bool:
        if not obj:
            return False
        return self._has_delivery_discount(obj)

    def get_assembly_promo_price(self, obj: Cart) -> Decimal:
        if not obj:
            return Decimal(0)
        assembly_price = self._get_assembly_price(obj)
        delivery_price = self._get_delivery_price_with_promo(obj)
        return Decimal(assembly_price + delivery_price)
