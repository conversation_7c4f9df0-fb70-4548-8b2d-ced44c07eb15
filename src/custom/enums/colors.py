import enum

from typing import TYPE_CHECKING

from django.utils.functional import Promise
from django.utils.translation import gettext_lazy as _

from pricing_v3.enums import PricingColorGroup

if TYPE_CHECKING:
    from custom.enums.enums import GenderEnum

NOT_PASSED = object()


class HiddenEnumMeta(enum.EnumMeta):
    def __iter__(cls):
        hidden = getattr(cls, '__hidden__', set())
        return (member for member in super().__iter__() if member.name not in hidden)


class ColorEnum(enum.IntEnum):
    @classmethod
    def choices(cls):
        return [(member.value, member.display_name) for member in cls]

    @classmethod
    def values(cls):
        return [int(member) for member in cls]

    @classmethod
    def get_deprecated_colors(cls):
        return []

    @classmethod
    def get_not_introduced_yet_colors(cls):
        return []

    @property
    def is_deprecated(self):
        return self in self.get_deprecated_colors()

    @property
    def is_not_yet_introduced(self):
        return self in self.get_not_introduced_yet_colors()

    @classmethod
    def get(cls, name, fallback=NOT_PASSED):
        try:
            return cls[name.replace(' ', '_').upper()]
        except KeyError:
            if fallback != NOT_PASSED:
                return fallback
            raise

    @classmethod
    def get_fallback(cls):
        return cls._missing_(None)

    @property
    def capacity_key(self):
        return 'capacity_{}'.format(self.slug)

    @property
    def slug(self):
        return 'color_{}'.format(self.value)

    @property
    def display_name(self):
        return self.name.replace('_', ' ').title()

    def get_fallbacks(self, include_self=False):
        return [self] if include_self else []

    @classmethod
    def get_default_shelf_type(cls):
        return None

    @property
    def color_name(self) -> str:
        raise NotImplementedError

    @classmethod
    def choices_active(cls):
        return [
            (member.value, member.display_name) for member in cls.get_active_colors()
        ]

    @classmethod
    def get_active_colors(cls):
        return [member for member in cls if member.is_active]

    @property
    def is_active(self):
        return not self.is_not_yet_introduced and not self.is_deprecated

    @classmethod
    def get_desk_available_colors(cls):
        return []

    # TODO: Unify translated_material_description and translated_color
    @property
    def translated_material_description(self):
        raise NotImplementedError

    @property
    def translated_color(self):
        raise NotImplementedError

    @property
    def translated_simple_color_key(self) -> str:
        raise NotImplementedError

    @property
    def translated_simple_color(self) -> Promise:
        return _(self.translated_simple_color_key)

    @property
    def hex(self):
        raise NotImplementedError

    @property
    def secondary_hex(self):
        return self.hex

    @property
    def is_multicolor(self):
        return False

    @classmethod
    def get_nude_colors(cls):
        return set()

    @classmethod
    def get_mild_colors(cls):
        return set()

    @classmethod
    def get_bold_colors(cls):
        return set()

    @classmethod
    def filter_by(cls, color_name: str) -> list:
        """Returns a list of enums filtered by color_name value.

        Returns:
            list: A list of enums that contain the keyword in their name.
        Example:
            Type03Color.filter_by('WHITE') gives us a list of all Type03 colors that
            contain 'WHITE' in their name
        """
        return [color for color in cls if color_name in color.name]

    def price_group(self):
        raise NotImplementedError

    @property
    def is_special_edition_color(self) -> bool:
        return False

    @classmethod
    def get_new_colors(cls) -> set:
        return set()

    def get_material_name(self):
        raise NotImplementedError

    def get_translated_simple_color_with_declination(
        self, gender: 'GenderEnum'
    ) -> Promise:
        from custom.enums.enums import GenderEnum

        if gender != GenderEnum.NONE:
            return _(f'{self.translated_simple_color_key}_{gender}')
        return _(self.translated_simple_color_key)


class Type01Color(ColorEnum):
    WHITE = 0
    BLACK = 1
    GREY = 3
    AUBERGINE = 4
    NATURAL = 5
    GRAY = GREY
    RED = 6
    YELLOW = 7
    DUSTY_PINK = 8
    BLUE = 9
    DARK_BROWN = 10
    MOSS_GREEN = 11

    def get_fallbacks(self, include_self=False):
        fallbacks = super(Type01Color, self).get_fallbacks(include_self)
        if self == self.GREY:
            fallbacks.append(self.GRAY)
        elif self == self.AUBERGINE:
            fallbacks.append(self.WHITE)
        elif self == self.NATURAL:
            fallbacks.append(self.WHITE)
        return fallbacks

    @classmethod
    def get_not_introduced_yet_colors(cls):
        return {}

    @classmethod
    def get_deprecated_colors(cls):
        return {
            cls.AUBERGINE,
            cls.NATURAL,
            cls.DARK_BROWN,
        }

    @classmethod
    def get_nude_colors(cls):
        return {
            cls.WHITE,
            cls.GREY,
        }

    @classmethod
    def get_mild_colors(cls):
        return {
            cls.DUSTY_PINK,
            cls.MOSS_GREEN,
        }

    @classmethod
    def get_bold_colors(cls):
        return {
            cls.BLACK,
            cls.RED,
            cls.YELLOW,
            cls.BLUE,
        }

    @property
    def color_name(self) -> str:
        return f'T01_{self.name}'

    @classmethod
    def _missing_(cls, value):
        return cls(cls.WHITE)

    @classmethod
    def get_desk_available_colors(cls):
        return {
            cls.WHITE,
            cls.BLACK,
            cls.GREY,
            cls.RED,
            cls.YELLOW,
            cls.DUSTY_PINK,
            cls.BLUE,
            cls.MOSS_GREEN,
        } & set(cls.get_active_colors())

    @property
    def translated_material_description(self):
        names = {
            self.WHITE: _('ivy_white_plywood'),
            self.BLACK: _('ivy_black_plywood'),
            self.GREY: _('ivy_grey_plywood'),
            self.AUBERGINE: _('ivy_purple_plywood'),
            self.NATURAL: _('ivy_ash_plywood'),  # obsolete
            self.RED: _('ivy_red_plywood'),
            self.YELLOW: _('ivy_yellow_plywood'),
            self.DUSTY_PINK: _('ivy_pink_plywood'),
            self.BLUE: _('ivy_blue_plywood'),
            self.DARK_BROWN: _('ivy_dark_brown_plywood'),
            self.MOSS_GREEN: _('ivy_mossgreen_plywood'),
        }
        return names.get(self, _('ivy_white_plywood'))

    @property
    def translated_color(self):
        names = {
            self.WHITE: _('ivy_white'),
            self.BLACK: _('ivy_black'),
            self.GREY: _('ivy_grey'),
            self.AUBERGINE: _('ivy_purple'),
            self.NATURAL: _('ivy_ash'),  # obsolete
            self.RED: _('ivy_red'),
            self.YELLOW: _('ivy_yellow'),
            self.DUSTY_PINK: _('ivy_pink'),
            self.BLUE: _('ivy_blue'),
            self.DARK_BROWN: _('ivy_dark_brown'),
            self.MOSS_GREEN: _('ivy_mossgreen'),
        }
        return names.get(self, _('ivy_white'))

    @property
    def translated_simple_color_key(self) -> str:
        return {
            self.WHITE: 'seo_plp_color_simple_t01_white',
            self.BLACK: 'seo_plp_color_simple_t01_black',
            self.GREY: 'seo_plp_color_simple_t01_grey',
            self.RED: 'seo_plp_color_simple_t01_classic_red',
            self.YELLOW: 'seo_plp_color_simple_t01_yellow',
            self.DUSTY_PINK: 'seo_plp_color_simple_t01_dusty_pink',
            self.BLUE: 'seo_plp_color_simple_t01_blue',
            self.DARK_BROWN: 'seo_plp_color_simple_t01_brown',
            self.MOSS_GREEN: 'seo_plp_color_simple_t01_mossgreen',
        }.get(self, 'seo_plp_color_simple_t01_white')

    @property
    def hex(self):
        hex_values = {
            self.WHITE: '#ffffff',
            self.BLACK: '#2D2D2D',
            2: '#dcccb7',
            self.GREY: '#8a847c',
            self.AUBERGINE: '#8a3799',
            self.NATURAL: '#f4ce42',
            self.RED: '#d40f1f',
            self.YELLOW: '#fff429',
            self.DUSTY_PINK: '#f5d3ec',
            self.BLUE: '#1f6ed4',
            self.DARK_BROWN: '#4a2e1e',
            self.MOSS_GREEN: '#869c68',
        }
        return hex_values.get(self, '#ffffff')

    @classmethod
    def get_new_colors(cls) -> set:
        return {cls.MOSS_GREEN}

    def price_group(self):
        return {
            self.WHITE: PricingColorGroup.group_1,
            self.BLACK: PricingColorGroup.group_1,
            self.GREY: PricingColorGroup.group_1,
            self.RED: PricingColorGroup.group_2,
            self.YELLOW: PricingColorGroup.group_2,
            self.DUSTY_PINK: PricingColorGroup.group_2,
            self.BLUE: PricingColorGroup.group_2,
            self.DARK_BROWN: PricingColorGroup.group_2,
            self.MOSS_GREEN: PricingColorGroup.group_2,
        }.get(self, PricingColorGroup.group_1)


class Type02Color(ColorEnum):
    WHITE = 0
    TERRACOTTA = 1
    MIDNIGHT_BLUE = 2
    SAND = 3  # sand beige + indigo blue banding
    MINT = 4
    MATTE_BLACK = 6
    SKY_BLUE = 7
    BURGUNDY = 8
    COTTON = 9
    GRAY = 10
    DARK_GRAY = 11  # gray + dark gray banding
    MUSTARD_YELLOW = 12  # sand beige + mustard yellow banding
    FOREST_GREEN = 13
    LILAC = 14
    REISINGERS_PINK = 15
    SAGE_GREEN = 16
    STONE_GRAY = 17
    WALNUT = 18  # stone gray + walnut banding

    @classmethod
    def get_deprecated_colors(cls):
        return [
            cls.MINT,
            cls.WALNUT,
            cls.MUSTARD_YELLOW,
        ]

    @classmethod
    def get_not_introduced_yet_colors(cls):
        return {
            cls.FOREST_GREEN,
            cls.LILAC,
        }

    @classmethod
    def get_nude_colors(cls):
        return {
            cls.WHITE,
            cls.COTTON,
            cls.GRAY,
            cls.DARK_GRAY,
            cls.STONE_GRAY,
        }

    @classmethod
    def get_mild_colors(cls):
        return {
            cls.SAND,
            cls.SKY_BLUE,
            cls.REISINGERS_PINK,
            cls.SAGE_GREEN,
        }

    @classmethod
    def get_bold_colors(cls):
        return {
            cls.TERRACOTTA,
            cls.MIDNIGHT_BLUE,
            cls.MATTE_BLACK,
            cls.BURGUNDY,
        }

    @classmethod
    def get_desk_available_colors(cls):
        return {
            cls.WHITE,
            cls.TERRACOTTA,
            cls.SAND,
            cls.COTTON,
            cls.GRAY,
            cls.DARK_GRAY,
            cls.REISINGERS_PINK,
            cls.SAGE_GREEN,
            cls.STONE_GRAY,
        } & set(cls.get_active_colors())

    @property
    def is_special_edition_color(self) -> bool:
        return self == self.REISINGERS_PINK

    @property
    def color_name(self) -> str:
        return f'T02_{self.name}'

    @classmethod
    def _missing_(cls, value):
        return cls(cls.WHITE)

    @property
    def translated_material_description(self):
        names = {
            self.WHITE: _('common_configurator_color_snow_white'),
            self.TERRACOTTA: _('common_configurator_color_terracotta'),
            self.MIDNIGHT_BLUE: _('common_configurator_color_midnight_blue'),
            self.SAND: _('common_configurator_color_sand'),
            self.MINT: _('common_configurator_color_mint'),
            self.MATTE_BLACK: _('common_matte_black'),
            self.SKY_BLUE: _('common_sky_blue'),
            self.BURGUNDY: _('common_burgundy_red'),
            self.COTTON: _('common_cotton_beige'),
            self.GRAY: _('common_grey_t02'),
            self.DARK_GRAY: _('common_grey_dark_grey'),
            self.MUSTARD_YELLOW: _('common_sand_mustard_yellow'),
            self.FOREST_GREEN: _('common_forest_green'),  # deprecated
            self.LILAC: _('common_lilac'),  # deprecated
            self.REISINGERS_PINK: _('common_reisingers_pink'),
            self.SAGE_GREEN: _('common_sage_green'),
            self.STONE_GRAY: _('common_stone_gray'),
            self.WALNUT: _('common_walnut'),
        }
        return names.get(self, _('common_configurator_color_snow_white'))

    @property
    def translated_simple_color_key(self) -> str:
        return {
            self.WHITE: 'seo_plp_color_simple_t02_white',
            self.TERRACOTTA: 'seo_plp_color_simple_t02_terracota',
            self.MIDNIGHT_BLUE: 'seo_plp_color_simple_t02_midnight_blue',
            self.SAND: 'seo_plp_color_simple_t02_sand_midnight_blue',
            self.MATTE_BLACK: 'seo_plp_color_simple_t02_matte_black',
            self.SKY_BLUE: 'seo_plp_color_simple_t02_sky_blue',
            self.BURGUNDY: 'seo_plp_color_simple_t02_burgundy_red',
            self.COTTON: 'seo_plp_color_simple_t02_cotton_beige',
            self.GRAY: 'seo_plp_color_simple_t02_grey',
            self.DARK_GRAY: 'seo_plp_color_simple_t02_grey_dark_grey',
            self.MUSTARD_YELLOW: 'seo_plp_color_simple_t02_sand_mustard_yellow',
            self.REISINGERS_PINK: 'seo_plp_color_simple_t02_reisinger_pink',
            self.SAGE_GREEN: 'seo_plp_color_simple_t02_green',
            self.STONE_GRAY: 'seo_plp_color_simple_t02_stone_grey',
            self.WALNUT: 'seo_plp_color_simple_t02_stone_grey_walnut',
        }.get(self, 'seo_plp_color_simple_t02_white')

    @property
    def translated_color(self):
        return self.translated_material_description

    @property
    def hex(self):
        hex_values = {
            self.WHITE: '#ffffff',
            self.TERRACOTTA: '#994027',
            self.MIDNIGHT_BLUE: '#272e3b',
            self.SAND: '#cebeae',
            self.MINT: '#becfc5',
            self.MATTE_BLACK: '#000000',
            self.SKY_BLUE: '#7D9CC0',
            self.BURGUNDY: '#800020',
            self.COTTON: '#fdf3ea',
            self.GRAY: '#808080',
            self.DARK_GRAY: '#808080',  # GRAY + DARK GRAY
            self.MUSTARD_YELLOW: '#cebeae',
            self.FOREST_GREEN: '#6a8547',
            self.LILAC: '#beb7bc',
            self.REISINGERS_PINK: '#e101c3',
            self.SAGE_GREEN: '#a3b5a6',
            self.STONE_GRAY: '#949494',
            self.WALNUT: '#949494',
        }
        return hex_values.get(self, '#ffffff')

    @property
    def secondary_hex(self):
        hex_values = {
            self.SAND: '#272e3b',
            self.DARK_GRAY: '#404040',
            self.MUSTARD_YELLOW: '#e1ad01',
            self.WALNUT: '#8a6e5d',
        }
        return hex_values.get(self, self.hex)

    @classmethod
    def get_new_colors(cls) -> set:
        return set()

    def price_group(self):
        return {
            self.WHITE: PricingColorGroup.group_1,
            self.TERRACOTTA: PricingColorGroup.group_3,
            self.MIDNIGHT_BLUE: PricingColorGroup.group_3,
            self.SAND: PricingColorGroup.group_2,
            self.MATTE_BLACK: PricingColorGroup.group_4,
            self.SKY_BLUE: PricingColorGroup.group_3,
            self.BURGUNDY: PricingColorGroup.group_3,
            self.COTTON: PricingColorGroup.group_1,
            self.GRAY: PricingColorGroup.group_1,
            self.DARK_GRAY: PricingColorGroup.group_2,
            self.MUSTARD_YELLOW: PricingColorGroup.group_2,
            self.REISINGERS_PINK: PricingColorGroup.group_4,
            self.SAGE_GREEN: PricingColorGroup.group_3,
            self.STONE_GRAY: PricingColorGroup.group_1,
            self.WALNUT: PricingColorGroup.group_2,
        }.get(self, PricingColorGroup.group_1)


class Type03Color(ColorEnum):
    WHITE = 0
    BEIGE = 1
    GRAPHITE = 2
    BEIGE_PINK = 3
    WHITE_PINK = 4
    GRAPHITE_WHITE = 5
    GRAPHITE_PINK = 6
    GRAPHITE_BEIGE = 7
    WHITE_BEIGE = 8
    WHITE_GRAPHITE = 9
    BEIGE_GRAPHITE = 10
    BEIGE_WHITE = 11
    GRAPHITE_STONE_GRAY = 12
    GRAPHITE_SAGE_GREEN = 13
    GRAPHITE_MISTY_BLUE = 14
    WHITE_STONE_GRAY = 15
    WHITE_SAGE_GREEN = 16
    WHITE_MISTY_BLUE = 17
    CASHMERE_STONE_GRAY = 18
    CASHMERE_SAGE_GREEN = 19
    CASHMERE_MISTY_BLUE = 20

    @property
    def color_name(self) -> str:
        return f'T03_{self.name}'

    @classmethod
    def _missing_(cls, value):
        return cls(cls.WHITE)

    @classmethod
    def get_not_introduced_yet_colors(cls):
        return [
            cls.GRAPHITE_WHITE,
            cls.GRAPHITE_BEIGE,
            cls.WHITE_BEIGE,
            cls.WHITE_GRAPHITE,
            cls.BEIGE_GRAPHITE,
            cls.BEIGE_WHITE,
        ]

    @classmethod
    def get_nude_colors(cls):
        return {
            cls.WHITE,
            cls.WHITE_SAGE_GREEN,
        }

    @classmethod
    def get_mild_colors(cls):
        return {
            cls.BEIGE,
            cls.BEIGE_PINK,
            cls.WHITE_PINK,
            cls.WHITE_STONE_GRAY,
            cls.WHITE_MISTY_BLUE,
            cls.CASHMERE_STONE_GRAY,
            cls.CASHMERE_SAGE_GREEN,
            cls.CASHMERE_MISTY_BLUE,
        }

    @classmethod
    def get_bold_colors(cls):
        return {
            cls.GRAPHITE,
            cls.GRAPHITE_PINK,
            cls.GRAPHITE_STONE_GRAY,
            cls.GRAPHITE_SAGE_GREEN,
            cls.GRAPHITE_MISTY_BLUE,
        }

    # TODO: construct dynamically
    @property
    def translated_material_description(self) -> str:
        names = {
            self.WHITE: _('watty_white'),
            self.BEIGE: _('watty_beige'),
            self.GRAPHITE: _('watty_graphite'),
            self.BEIGE_PINK: _('watty_beige_pink'),
            self.WHITE_PINK: _('watty_white_pink'),
            self.GRAPHITE_WHITE: _('watty_graphite_white'),  # deprecated
            self.GRAPHITE_PINK: _('watty_graphite_pink'),
            self.GRAPHITE_BEIGE: _('watty_graphite_beige'),  # deprecated
            self.WHITE_BEIGE: _('watty_white_beige'),  # deprecated
            self.WHITE_GRAPHITE: _('watty_white_graphite'),  # deprecated
            self.BEIGE_GRAPHITE: _('watty_beige_graphite'),  # deprecated
            self.BEIGE_WHITE: _('watty_beige_white'),  # deprecated
            self.GRAPHITE_STONE_GRAY: _('watty_graphite_stone_gray'),
            self.GRAPHITE_SAGE_GREEN: _('watty_graphite_sage_green'),
            self.GRAPHITE_MISTY_BLUE: _('watty_graphite_misty_blue'),
            self.WHITE_STONE_GRAY: _('watty_white_stone_gray'),
            self.WHITE_SAGE_GREEN: _('watty_white_sage_green'),
            self.WHITE_MISTY_BLUE: _('watty_white_misty_blue'),
            self.CASHMERE_STONE_GRAY: _('watty_cashmere_stone_gray'),
            self.CASHMERE_SAGE_GREEN: _('watty_cashmere_sage_green'),
            self.CASHMERE_MISTY_BLUE: _('watty_cashmere_misty_blue'),
        }
        return names.get(self, _('watty_white'))

    @property
    def translated_simple_color_key(self) -> str:
        return {
            self.WHITE: 'seo_plp_color_simple_t03_white',
            self.BEIGE: 'seo_plp_color_simple_t03_cashmere_beige',
            self.GRAPHITE: 'seo_plp_color_simple_t03_graphite_grey',
            self.BEIGE_PINK: 'seo_plp_color_simple_t03_cashmere_antique_pink',
            self.WHITE_PINK: 'seo_plp_color_simple_t03_white_antique_pink',
            self.GRAPHITE_PINK: 'seo_plp_color_simple_t03_graphite_grey_antique_pink',
            self.GRAPHITE_STONE_GRAY: 'seo_plp_color_simple_t03_graphite_stone_gray',
            self.GRAPHITE_SAGE_GREEN: 'seo_plp_color_simple_t03_graphite_sage_green',
            self.GRAPHITE_MISTY_BLUE: 'seo_plp_color_simple_t03_graphite_misty_blue',
            self.WHITE_STONE_GRAY: 'seo_plp_color_simple_t03_white_stone_gray',
            self.WHITE_SAGE_GREEN: 'seo_plp_color_simple_t03_white_sage_green',
            self.WHITE_MISTY_BLUE: 'seo_plp_color_simple_t03_white_misty_blue',
            self.CASHMERE_STONE_GRAY: 'seo_plp_color_simple_t03_cashmere_stone_gray',
            self.CASHMERE_SAGE_GREEN: 'seo_plp_color_simple_t03_cashmere_sage_green',
            self.CASHMERE_MISTY_BLUE: 'seo_plp_color_simple_t03_cashmere_misty_blue',
        }.get(self, 'seo_plp_color_simple_t03_white')

    @property
    def translated_color(self):
        return self.translated_material_description

    @property
    def hex(self) -> str:
        white = '#ffffff'
        beige = '#F5F5DC'
        graphite = '#474a51'
        cashmere = '#C7BFB7'
        hex_values = {
            self.WHITE: white,
            self.BEIGE: beige,
            self.GRAPHITE: graphite,
            self.BEIGE_PINK: beige,
            self.WHITE_PINK: white,
            self.GRAPHITE_WHITE: graphite,
            self.GRAPHITE_PINK: graphite,
            self.GRAPHITE_BEIGE: graphite,
            self.WHITE_BEIGE: white,
            self.WHITE_GRAPHITE: white,
            self.BEIGE_GRAPHITE: beige,
            self.BEIGE_WHITE: beige,
            self.GRAPHITE_STONE_GRAY: graphite,
            self.GRAPHITE_SAGE_GREEN: graphite,
            self.GRAPHITE_MISTY_BLUE: graphite,
            self.WHITE_STONE_GRAY: white,
            self.WHITE_SAGE_GREEN: white,
            self.WHITE_MISTY_BLUE: white,
            self.CASHMERE_STONE_GRAY: cashmere,
            self.CASHMERE_SAGE_GREEN: cashmere,
            self.CASHMERE_MISTY_BLUE: cashmere,
        }
        return hex_values.get(self, '#ffffff')

    @property
    def secondary_hex(self):
        white = '#ffffff'
        beige = '#F5F5DC'
        graphite = '#474a51'
        pink = '#FFA0A0'
        stone_gray = '#A4998F'
        sage_green = '#A9D39E'
        misty_blue = '#96a6b5'
        hex_values = {
            self.BEIGE_PINK: pink,
            self.WHITE_PINK: pink,
            self.GRAPHITE_WHITE: white,
            self.GRAPHITE_PINK: pink,
            self.GRAPHITE_BEIGE: beige,
            self.WHITE_BEIGE: beige,
            self.WHITE_GRAPHITE: graphite,
            self.BEIGE_GRAPHITE: graphite,
            self.BEIGE_WHITE: white,
            self.GRAPHITE_STONE_GRAY: stone_gray,
            self.GRAPHITE_SAGE_GREEN: sage_green,
            self.GRAPHITE_MISTY_BLUE: misty_blue,
            self.WHITE_STONE_GRAY: stone_gray,
            self.WHITE_SAGE_GREEN: sage_green,
            self.WHITE_MISTY_BLUE: misty_blue,
            self.CASHMERE_STONE_GRAY: stone_gray,
            self.CASHMERE_SAGE_GREEN: sage_green,
            self.CASHMERE_MISTY_BLUE: misty_blue,
        }
        return hex_values.get(self, self.hex)

    @property
    def is_multicolor(self) -> bool:
        """Returns True if the wardrobe has a different color outside than inside."""
        multicolors = {
            self.BEIGE_PINK: True,
            self.WHITE_PINK: True,
            self.GRAPHITE_WHITE: True,
            self.GRAPHITE_PINK: True,
            self.GRAPHITE_BEIGE: True,
            self.WHITE_BEIGE: True,
            self.WHITE_GRAPHITE: True,
            self.BEIGE_GRAPHITE: True,
            self.BEIGE_WHITE: True,
            self.GRAPHITE_STONE_GRAY: True,
            self.GRAPHITE_SAGE_GREEN: True,
            self.GRAPHITE_MISTY_BLUE: True,
            self.WHITE_STONE_GRAY: True,
            self.WHITE_SAGE_GREEN: True,
            self.WHITE_MISTY_BLUE: True,
            self.CASHMERE_STONE_GRAY: True,
            self.CASHMERE_SAGE_GREEN: True,
            self.CASHMERE_MISTY_BLUE: True,
        }
        return multicolors.get(self, False)

    def get_material_name(self):
        return {
            self.WHITE: 'Egger Alpine White W1100 ST9',
            self.BEIGE: 'Kaszmir - Egger U702 PM/ST9',
            self.GRAPHITE: 'Grafit - Egger U961 PM/ST9',
            self.BEIGE_PINK: 'Kaszmir / Roz - Egger U702 PM/ST9',
            self.WHITE_PINK: 'Biały/róż - Egger W1100/U325 PM/ST9',
            self.GRAPHITE_WHITE: 'Grafit/biały - Egger U961/W1100 PM/ST9',
            self.GRAPHITE_PINK: 'Grafit/róż - Egger U961/U325 PM/ST9',
            self.GRAPHITE_BEIGE: 'Grafit/kaszmir - Egger U961/U325 PM/ST9',
            self.WHITE_BEIGE: 'Biały/kaszmir - Egger W1100/U702 PM/ST9',
            self.WHITE_GRAPHITE: 'Biały/grafit - Egger W1100/U961 PM/ST9',
            self.BEIGE_GRAPHITE: 'Kaszmir/grafit - Egger U702/U961 PM/ST9',
            self.BEIGE_WHITE: 'Kaszmir/biały - Egger U702/W1100 PM/ST9',
            self.GRAPHITE_STONE_GRAY: 'Grafit/szary kamienny - Egger U961/U727 ST9/ST9',
            self.GRAPHITE_SAGE_GREEN: 'Grafit/szałwiowy - Egger U961/U608 ST9/ST9',
            self.GRAPHITE_MISTY_BLUE: (
                'Grafit/mglisty niebieski - Egger U961/U540 ST9/ST9'
            ),
            self.WHITE_STONE_GRAY: 'Biały/szary kamienny - Egger W1100/U727 PM/ST9',
            self.WHITE_SAGE_GREEN: 'Biały/szałwiowy - Egger W1100/U608 PM/ST9',
            self.WHITE_MISTY_BLUE: 'Biały/mglisty niebieski - Egger W1100/U540 PM/ST9',
            self.CASHMERE_STONE_GRAY: (
                'Kaszmir/szary kamienny - Egger U702/U727 ST9/ST9'
            ),
            self.CASHMERE_SAGE_GREEN: 'Kaszmir/szałwiowy - Egger U702/U608 ST9/ST9',
            self.CASHMERE_MISTY_BLUE: (
                'Kaszmir/mglisty niebieski - Egger U702/U540 ST9/ST9'
            ),
        }[self]


class Type03ExteriorInteriorColor(ColorEnum):
    # used only in samples
    EXTERIOR_WHITE = 0
    EXTERIOR_BEIGE = 1
    EXTERIOR_GRAPHITE = 2
    WHITE = 3
    BEIGE = 4
    GRAPHITE = 5
    PINK = 6
    STONE_GRAY = 7
    SAGE_GREEN = 8
    MISTY_BLUE = 9

    @property
    def exterior(self):
        return self in {
            self.EXTERIOR_WHITE,
            self.EXTERIOR_BEIGE,
            self.EXTERIOR_GRAPHITE,
        }


class VeneerType01Color(ColorEnum):
    ASH = 0
    OAK = 1
    DARK_OAK = 2

    @classmethod
    def get_nude_colors(cls):
        return set()

    @classmethod
    def get_mild_colors(cls):
        return {
            cls.ASH,
            cls.OAK,
        }

    @classmethod
    def get_bold_colors(cls):
        return {cls.DARK_OAK}

    @classmethod
    def get_new_colors(cls) -> set:
        return set()

    @property
    def color_name(self) -> str:
        return f'T01_{self.name}'

    @classmethod
    def _missing_(cls, value):
        return cls(cls.ASH)

    # TODO: Unify translated_material_description and translated_color
    @property
    def translated_material_description(self):
        names = {
            self.ASH: _('veneer_shelf_ash'),
            self.OAK: _('veneer_shelf_oak'),
            self.DARK_OAK: _('veneer_shelf_dark_oak'),
        }
        return names.get(self, _('veneer_shelf_ash'))

    @property
    def translated_color_key(self) -> str:
        return {
            self.ASH: 'common_configurator_color_veneer_ash',
            self.OAK: 'common_configurator_color_veneer_oak',
            self.DARK_OAK: 'common_configurator_color_veneer_dark_oak',
        }.get(self, 'common_configurator_color_veneer_ash')

    @property
    def translated_color(self) -> Promise:
        return _(self.translated_color_key)

    @property
    def translated_simple_color_key(self) -> str:
        return self.translated_color_key

    @property
    def hex(self):
        hex_values = {
            self.ASH: '#f7e9da',
            self.OAK: '#d4b285',
            self.DARK_OAK: '#6f5339',
        }
        return hex_values.get(self, '#ffffff')

    def price_group(self):
        return PricingColorGroup.group_1


class Type13Color(ColorEnum):
    WHITE = 0
    SAND = 1
    MUSTARD_YELLOW = 2
    GRAY = 3
    DARK_GRAY = 4
    WHITE_PLYWOOD = 5
    GRAY_PLYWOOD = 6
    BLACK_PLYWOOD = 7
    CLAY_BROWN = 8
    OLIVE_GREEN = 9
    BEIGE = 10  # without contrasting banding, as opposed to SAND
    BLACK = 11

    @classmethod
    def get_deprecated_colors(cls):
        return [
            cls.DARK_GRAY,
            cls.MUSTARD_YELLOW,
            cls.SAND,
            cls.BLACK_PLYWOOD,
        ]

    @classmethod
    def get_nude_colors(cls):
        return {
            cls.WHITE,
            cls.GRAY,
            cls.WHITE_PLYWOOD,
            cls.GRAY_PLYWOOD,
        }

    @classmethod
    def get_mild_colors(cls):
        return {
            cls.BEIGE,
        }

    @classmethod
    def get_bold_colors(cls):
        return {
            cls.CLAY_BROWN,
            cls.OLIVE_GREEN,
            cls.BLACK,
        }

    @property
    def color_name(self) -> str:
        return f'W13_{self.name}'

    @classmethod
    def _missing_(cls, value):
        return cls(cls.WHITE)

    @property
    def translated_material_description(self):
        names = {
            self.WHITE: _('watty_white'),
            self.SAND: _('common_configurator_color_sand'),
            self.MUSTARD_YELLOW: _('common_sand_mustard_yellow'),
            self.GRAY: _('common_grey_t02'),
            self.DARK_GRAY: _('common_grey_dark_grey'),
            self.WHITE_PLYWOOD: _('ivy_white_plywood'),
            self.GRAY_PLYWOOD: _('ivy_grey_plywood'),
            self.BLACK_PLYWOOD: _('ivy_black_plywood'),
            self.CLAY_BROWN: _('common_clay_brown'),
            self.OLIVE_GREEN: _('common_olive_green'),
            self.BEIGE: _('common_beige'),
            self.BLACK: _('common_black'),
        }
        return names.get(self, _('watty_white'))

    @property
    def translated_color(self):
        return self.translated_material_description

    @property
    def translated_simple_color_key(self) -> str:
        return {
            self.WHITE: 'seo_plp_color_simple_t13_white',
            self.SAND: 'seo_plp_color_simple_t13_sand_midnight_blue',
            self.MUSTARD_YELLOW: 'seo_plp_color_simple_t13_sand_mustard_yellow',
            self.GRAY: 'seo_plp_color_simple_t13_grey',
            self.DARK_GRAY: 'seo_plp_color_simple_t13_grey_dark_grey',
            self.WHITE_PLYWOOD: 'seo_plp_color_simple_t13_white',
            self.GRAY_PLYWOOD: 'seo_plp_color_simple_t13_grey',
            self.BLACK_PLYWOOD: 'seo_plp_color_simple_t13_black',
            self.CLAY_BROWN: 'seo_plp_color_simple_t13_clay_brown',
            self.OLIVE_GREEN: 'seo_plp_color_simple_t13_olive_green',
            self.BEIGE: 'seo_plp_color_simple_t13_beige',
            self.BLACK: 'seo_plp_color_simple_t13_black',
        }.get(self, 'seo_plp_color_simple_t13_white')

    @property
    def hex(self):
        hex_values = {
            self.WHITE: '#ffffff',
            self.SAND: '#cebeae',
            self.MUSTARD_YELLOW: '#cebeae',
            self.GRAY: '#949494',
            self.DARK_GRAY: '#949494',
            self.WHITE_PLYWOOD: '#ffffff',
            self.GRAY_PLYWOOD: '#8a847c',
            self.BLACK_PLYWOOD: '#2D2D2D',
            self.CLAY_BROWN: '#c7875d',
            self.OLIVE_GREEN: '#9EA685',
            self.BEIGE: '#EAE3C9',
            self.BLACK: '#1A1A1A',
        }
        return hex_values.get(self, '#ffffff')

    @property
    def secondary_hex(self):
        hex_values = {
            self.SAND: '#272e3b',
            self.DARK_GRAY: '#404040',
            self.MUSTARD_YELLOW: '#e1ad01',
        }
        return hex_values.get(self, self.hex)

    @property
    def is_plywood_color(self):
        return self in {
            Type13Color.BLACK_PLYWOOD,
            Type13Color.GRAY_PLYWOOD,
            Type13Color.WHITE_PLYWOOD,
        }

    def get_material_name(self):
        return {
            self.WHITE: 'Snow White - Kronospan 8685 BS',
            self.SAND: 'Sand Beige - Egger U156 ST9',
            self.MUSTARD_YELLOW: 'Mustard yellow - Egger U156 ST9',
            self.GRAY: 'Gray - EGGER U773 ST9',
            self.DARK_GRAY: 'Dark gray - EGGER U773 ST9',
            self.WHITE_PLYWOOD: 'White-plywood - Biala sklejka',
            self.GRAY_PLYWOOD: 'Gray-plywood - Szara sklejka',
            self.BLACK_PLYWOOD: 'Black-plywood - Czarna sklejka',
            self.CLAY_BROWN: 'Clay Brown - Egger U830 ST9',
            self.OLIVE_GREEN: 'Olive Green - Fundermax 0041 FH (Star Favorit P2)',
            self.BEIGE: 'Beige - Egger U156 ST9',
            self.BLACK: 'Black - Egger U999 ST9',
        }[self]


class VeneerType13Color(ColorEnum):
    LIGHT = 0
    DARK = 1

    @classmethod
    def get_nude_colors(cls):
        return set()

    @classmethod
    def get_mild_colors(cls):
        return {cls.LIGHT}

    @classmethod
    def get_bold_colors(cls):
        return {cls.DARK}

    @classmethod
    def get_new_colors(cls) -> set:
        return set()

    def get_material_name(self):
        return {
            self.LIGHT: 'CLEAF QUERCIA S 206',
            self.DARK: 'CLEAF QUERCIA S 185',
        }[self]

    @property
    def hex(self):
        return {
            self.LIGHT: '#b5a991',
            self.DARK: '#47290e',
        }[self]

    @property
    def translated_material_description(self):
        names = {
            self.LIGHT: _('veneer_watty_light'),
            self.DARK: _('veneer_watty_dark'),
        }
        return names.get(self, _('veneer_watty_light'))

    @property
    def translated_color(self):
        return self.translated_material_description

    @property
    def is_plywood_color(self):
        return False

    @property
    def translated_simple_color_key(self) -> str:
        return {
            self.LIGHT: 'seo_plp_color_simple_t13v_light',
            self.DARK: 'seo_plp_color_simple_t13v_dark',
        }.get(self, 'seo_plp_color_simple_t13v_light')


class CommonType2xColor(ColorEnum):
    """Type 23, Type 24 and Type 25 have common colors."""

    OFF_WHITE: int
    OYSTER_BEIGE: int
    PISTACHIO_GREEN: int
    INKY_BLACK: int
    POWDER_PINK: int

    @classmethod
    def _missing_(cls, value):
        return cls(cls.OFF_WHITE)

    @classmethod
    def get_nude_colors(cls):
        return {cls.OFF_WHITE, cls.OYSTER_BEIGE}

    @classmethod
    def get_mild_colors(cls):
        return {cls.INKY_BLACK, cls.PISTACHIO_GREEN, cls.POWDER_PINK}

    @classmethod
    def get_bold_colors(cls):
        return set()

    @classmethod
    def get_new_colors(cls) -> set:
        return set()

    def get_material_name(self):
        return {
            self.OFF_WHITE: 'Forner Velvet Ultra Matt Porcelanowy 1551/PP10',
            self.OYSTER_BEIGE: 'Forner Velvet Ultra Matt Jasny Beżowy 7393/PP8',
            self.PISTACHIO_GREEN: (
                'Płyta Wiórowa Forner Velvet Ultra Matt Oliwka 3703/PP8'
            ),
            self.INKY_BLACK: (
                'Płyta Wiórowa Forner Velvet Ultra Matt Granatowy 6221/PP6'
            ),
            self.POWDER_PINK: 'Forner Velvet Ultra Matt Różowy 5987/PP8',
        }[self]

    @property
    def translated_material_description(self) -> str:
        names = {
            self.OFF_WHITE: _('common_configurator_color_off_white'),
            self.OYSTER_BEIGE: _('common_configurator_color_oyster_beige'),
            self.PISTACHIO_GREEN: _('common_configurator_color_pistachio_green'),
            self.INKY_BLACK: _('common_configurator_color_inky_black'),
            self.POWDER_PINK: _('common_configurator_color_powder_pink'),
        }
        return names.get(self, _('common_configurator_color_off_white'))

    @property
    def translated_simple_color_key(self) -> str:
        return {
            self.OFF_WHITE: 'seo_plp_color_simple_t23_off_white',
            self.OYSTER_BEIGE: 'seo_plp_color_simple_t23_oyster_beige',
            self.PISTACHIO_GREEN: 'seo_plp_color_simple_t23_pistachio_green',
            self.INKY_BLACK: 'seo_plp_color_simple_t23_inky_black',
            self.POWDER_PINK: 'seo_plp_color_simple_t23_powder_pink',
        }.get(self, 'seo_plp_color_simple_t23_off_white')

    @property
    def translated_color(self):
        return self.translated_material_description

    @property
    def hex(self) -> str:
        hex_values = {
            self.OFF_WHITE: '#ffffff',
            self.OYSTER_BEIGE: '#e7e3da',
            self.PISTACHIO_GREEN: '#d0d5b4',
            self.INKY_BLACK: '#3f404d',
            self.POWDER_PINK: '#d6beb9',
        }
        return hex_values.get(self, '#ffffff')


class Type23Color(CommonType2xColor):

    OFF_WHITE = 0
    OYSTER_BEIGE = 1
    PISTACHIO_GREEN = 2
    INKY_BLACK = 3
    POWDER_PINK = 4

    @property
    def color_name(self) -> str:
        return f'T23_{self.name}'


class Type24Color(CommonType2xColor):

    OFF_WHITE = 0
    OYSTER_BEIGE = 1
    PISTACHIO_GREEN = 2
    INKY_BLACK = 3
    POWDER_PINK = 4

    @property
    def color_name(self) -> str:
        return f'T24_{self.name}'


class Type25Color(CommonType2xColor):

    OFF_WHITE = 0
    OYSTER_BEIGE = 1
    PISTACHIO_GREEN = 2
    INKY_BLACK = 3
    POWDER_PINK = 4

    @property
    def color_name(self) -> str:
        return f'T25_{self.name}'


class Sofa01Color(ColorEnum, metaclass=HiddenEnumMeta):
    __hidden__ = {'MULTICOLOR'}
    # Do not change enum names, they are used in communication with producer
    # Names used in comment are the one shown to users
    REWOOL2_BROWN = 0  # dark_brown
    REWOOL2_OLIVE_GREEN = 1  # khaki
    REWOOL2_LIGHT_GRAY = 2  # grey
    REWOOL2_BUTTER_YELLOW = 3  # pale_yellow
    REWOOL2_SHADOW_PINK = 4  # powder_pink
    REWOOL2_GREEN = 5  # green
    REWOOL2_BABY_BLUE = 6  # lilac_blue
    
    CORDUROY_ECRU = 7  # off_white
    CORDUROY_ROCK = 8  # beige
    CORDUROY_DARK_BROWN = 9  # brown
    CORDUROY_STEEL = 10  # light_grey
    CORDUROY_TOBACCO = 11  # mustard
    CORDUROY_PINK = 12  # blush_pink
    CORDUROY_CAMOUFLAGE = 13  # olive_green
    CORDUROY_BLUE_KLEIN = 14  # cobalt_blue

    # use it only explicit
    MULTICOLOR = -1

    @classmethod
    def get_wool_colors(cls) -> set:
        return {
            cls.REWOOL2_BROWN,
            cls.REWOOL2_OLIVE_GREEN,
            cls.REWOOL2_LIGHT_GRAY,
            cls.REWOOL2_BUTTER_YELLOW,
            cls.REWOOL2_SHADOW_PINK,
            cls.REWOOL2_GREEN,
            cls.REWOOL2_BABY_BLUE,
        }

    @classmethod
    def get_corduroy_colors(cls):
        return {
            cls.CORDUROY_ECRU,
            cls.CORDUROY_ROCK,
            cls.CORDUROY_DARK_BROWN,
            cls.CORDUROY_STEEL,
            cls.CORDUROY_TOBACCO,
            cls.CORDUROY_PINK,
            cls.CORDUROY_CAMOUFLAGE,
            cls.CORDUROY_BLUE_KLEIN,
        }

    @property
    def color_name(self) -> str:
        return f'S01_{self.name}'

    @classmethod
    def _missing_(cls, value):
        return cls(cls.REWOOL2_BROWN)

    @property
    def hex(self):
        hex_values = {
            self.REWOOL2_BROWN: '#85200C',
            self.REWOOL2_OLIVE_GREEN: '#93C47D',
            self.REWOOL2_LIGHT_GRAY: '#D9D9D9',
            self.REWOOL2_BUTTER_YELLOW: '#FFD966',
            self.REWOOL2_SHADOW_PINK: '#D5A6BD',
            self.REWOOL2_GREEN: '#6AA84F',
            self.REWOOL2_BABY_BLUE: '#A4C2F4',
            self.CORDUROY_ECRU: '#FFF7E1',
            self.CORDUROY_ROCK: '#DDD9CC',
            self.CORDUROY_DARK_BROWN: '#783F04',
            self.CORDUROY_STEEL: '#CCCCCC',
            self.CORDUROY_TOBACCO: '#F6B26B',
            self.CORDUROY_PINK: '#D5A6BD',
            self.CORDUROY_CAMOUFLAGE: '#657454',
            self.CORDUROY_BLUE_KLEIN: '#1155CC',
        }
        return hex_values.get(self, '#ffffff')

    def get_material_name(self):
        # FIXME
        return self.name

    @property
    def translated_material_description(self):
        names = {
            self.REWOOL2_BROWN: _('color_s01_rewool2_dark_brown'),
            self.REWOOL2_OLIVE_GREEN: _('color_s01_rewool2_khaki'),
            self.REWOOL2_LIGHT_GRAY: _('color_s01_rewool2_grey'),
            self.REWOOL2_BUTTER_YELLOW: _('color_s01_rewool2_pale_yellow'),
            self.REWOOL2_SHADOW_PINK: _('color_s01_rewool2_powder_pink'),
            self.REWOOL2_GREEN: _('color_s01_rewool2_green'),
            self.REWOOL2_BABY_BLUE: _('color_s01_rewool2_lilac_blue'),
            self.CORDUROY_ECRU: _('color_s01_corduroy_off_white'),
            self.CORDUROY_ROCK: _('color_s01_corduroy_beige'),
            self.CORDUROY_DARK_BROWN: _('color_s01_corduroy_brown'),
            self.CORDUROY_STEEL: _('color_s01_corduroy_light_grey'),
            self.CORDUROY_TOBACCO: _('color_s01_corduroy_mustard'),
            self.CORDUROY_PINK: _('color_s01_corduroy_blush_pink'),
            self.CORDUROY_CAMOUFLAGE: _('color_s01_corduroy_olive_green'),
            self.CORDUROY_BLUE_KLEIN: _('color_s01_corduroy_cobalt_blue'),
            self.MULTICOLOR: _('color_s01_multicolor'),
        }
        return names.get(self, _('color_s01_rewool2_dark_brown'))

    @property
    def translated_color(self):
        return self.translated_material_description

    @property
    def translated_simple_color_key(self) -> str:
        return {
            self.REWOOL2_BROWN: 'seo_plp_color_simple_s01_rewool2_dark_brown',
            self.REWOOL2_OLIVE_GREEN: 'seo_plp_color_simple_s01_rewool2_khaki',
            self.REWOOL2_LIGHT_GRAY: 'seo_plp_color_simple_s01_rewool2_grey',
            self.REWOOL2_BUTTER_YELLOW: 'seo_plp_color_simple_s01_rewool2_pale_yellow',
            self.REWOOL2_SHADOW_PINK: 'seo_plp_color_simple_s01_rewool2_powder_pink',
            self.REWOOL2_GREEN: 'seo_plp_color_simple_s01_rewool2_green',
            self.REWOOL2_BABY_BLUE: 'seo_plp_color_simple_s01_rewool2_lilac_blue',
            self.CORDUROY_ECRU: 'seo_plp_color_simple_s01_corduroy_off_white',
            self.CORDUROY_ROCK: 'seo_plp_color_simple_s01_corduroy_beige',
            self.CORDUROY_DARK_BROWN: 'seo_plp_color_simple_s01_corduroy_brown',
            self.CORDUROY_STEEL: 'seo_plp_color_simple_s01_corduroy_light_grey',
            self.CORDUROY_TOBACCO: 'seo_plp_color_simple_s01_corduroy_mustard',
            self.CORDUROY_PINK: 'seo_plp_color_simple_s01_corduroy_blush_pink',
            self.CORDUROY_CAMOUFLAGE: 'seo_plp_color_simple_s01_corduroy_olive_green',
            self.CORDUROY_BLUE_KLEIN: 'seo_plp_color_simple_s01_corduroy_cobalt_blue',
            self.MULTICOLOR: 'seo_plp_color_simple_s01_multicolor',
        }.get(self, 'seo_plp_color_simple_s01_rewool2_dark_brown')

    @property
    def is_plywood_color(self):
        return False

    @classmethod
    def get_nude_colors(cls):
        return {
            cls.REWOOL2_LIGHT_GRAY,
            cls.CORDUROY_ECRU,
            cls.CORDUROY_STEEL,
            cls.CORDUROY_ROCK,
        }

    @classmethod
    def get_mild_colors(cls):
        return {
            cls.CORDUROY_PINK,
            cls.REWOOL2_SHADOW_PINK,
            cls.REWOOL2_BUTTER_YELLOW,
            cls.REWOOL2_OLIVE_GREEN,
            cls.CORDUROY_CAMOUFLAGE,
            cls.REWOOL2_BABY_BLUE,
        }

    @classmethod
    def get_bold_colors(cls):
        return {
            cls.REWOOL2_GREEN,
            cls.CORDUROY_BLUE_KLEIN,
            cls.CORDUROY_DARK_BROWN,
            cls.CORDUROY_TOBACCO,
            cls.REWOOL2_BROWN,
        }
