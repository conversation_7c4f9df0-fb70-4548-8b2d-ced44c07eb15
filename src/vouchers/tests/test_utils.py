from decimal import Decimal

from django.contrib.auth import get_user_model

import pytest

from vouchers.enums import (
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import Voucher
from vouchers.utils import (
    convert_absolute_voucher_to_percentage,
    create_b2b_vouchers,
    get_or_create_klarna_absolute_discount_voucher,
)

User = get_user_model()


# It fails time to time, maybe because of the `.bulk_create()` method
# but too many tries where done to keep pushing it
@pytest.mark.skip
def test_should_create_b2b_vouchers(user_factory, voucher_settings):
    user_factory(username='admin')

    b2b_codes = create_b2b_vouchers(quantity=2)

    assert (
        Voucher.objects.filter(
            code__in=b2b_codes,
            kind_of=VoucherType.PERCENTAGE,
            quantity=10,
            quantity_left=10,
            origin=VoucherOrigin.B2B,
            value=Decimal('42'),
            amount_starts=Decimal('0'),
            amount_limit=Decimal('1000000'),
        ).count()
        == 2
    )


@pytest.mark.django_db
class TestConvertAbsoluteVoucherToPercentage:
    def test_should_convert_when_before_promo_not_equal_zero(self, order_factory):
        order = order_factory(
            region_total_price=Decimal('900.0'),
            region_promo_amount=Decimal('100.0'),
        )

        promo_value_rate = convert_absolute_voucher_to_percentage(order)
        assert promo_value_rate == Decimal('0.1')

    def test_should_convert_when_before_promo_zero(self, order_factory):
        order = order_factory(
            region_total_price=Decimal('0.0'),
            region_promo_amount=Decimal('0.0'),
        )

        promo_value_rate = convert_absolute_voucher_to_percentage(order)
        assert promo_value_rate == Decimal('1')

    def test_should_convert_without_region_assembly_when_no_assembly(
        self,
        order_factory,
        order_item_factory,
    ):
        order = order_factory(
            region_total_price=Decimal('800.0'),
            region_promo_amount=Decimal('200.0'),
            assembly=False,
            items=[],
        )
        order_item_factory(
            order=order,
            region_price=Decimal('300.0'),
            region_assembly_price=Decimal('100.0'),
        )
        order_item_factory(
            order=order,
            region_price=Decimal('400.0'),
            region_assembly_price=Decimal('300.0'),
        )

        promo_value_rate = convert_absolute_voucher_to_percentage(order)
        assert promo_value_rate == Decimal('0.2')

    def test_should_convert_with_region_assembly_when_assembly(
        self,
        order_factory,
        order_item_factory,
    ):
        order = order_factory(
            region_total_price=Decimal('800.0'),
            region_promo_amount=Decimal('200.0'),
            assembly=True,
            items=[],
        )
        order_item_factory(
            order=order,
            region_price=Decimal('200.0'),
            region_assembly_price=Decimal('100.0'),
        )
        order_item_factory(
            order=order,
            region_price=Decimal('600.0'),
            region_assembly_price=Decimal('100.0'),
        )

        promo_value_rate = convert_absolute_voucher_to_percentage(order)
        assert promo_value_rate == Decimal('0.25')

    def test_should_convert_with_region_assembly_when_assembly_and_multiple_quantity(
        self,
        order_factory,
        order_item_factory,
    ):
        order = order_factory(
            region_total_price=Decimal('1200.0'),
            region_promo_amount=Decimal('400.0'),
            assembly=True,
            items=[],
        )
        order_item_factory(
            order=order,
            region_price=Decimal('200.0'),
            region_assembly_price=Decimal('100.0'),
            quantity=2,
        )
        order_item_factory(
            order=order,
            region_price=Decimal('300.0'),
            region_assembly_price=Decimal('200.0'),
            quantity=2,
        )

        promo_value_rate = convert_absolute_voucher_to_percentage(order)
        assert promo_value_rate == Decimal('0.40')


@pytest.mark.django_db
class TestGetOrCreateKlarnaAbsoluteVoucher:
    def test_get_or_create_klarna_absolute_discount_voucher_should_create_region_entry(
        self,
        region_factory,
        currency_factory,
        currency_rate_factory,
        admin_user,
    ):
        zloty_currency = currency_factory(name='zloty', code='PLN')
        currency_rate_factory(rate=Decimal('4.5'), currency=zloty_currency)
        poland = region_factory(
            name='Poland',
            currency=zloty_currency,
        )
        klarna_voucher = get_or_create_klarna_absolute_discount_voucher(
            region=poland,
            region_voucher_value=Decimal('100.0'),
        )
        klarna_voucher_region_entry = klarna_voucher.region_entries.get(region=poland)
        assert klarna_voucher.value == Decimal('22')
        assert klarna_voucher.code == 'klarna_22'
        assert klarna_voucher_region_entry.value == Decimal('100.0')
        assert klarna_voucher_region_entry.region == poland
