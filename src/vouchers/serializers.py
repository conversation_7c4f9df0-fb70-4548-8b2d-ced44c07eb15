import typing

from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decima<PERSON>
from typing import ClassVar

from django.contrib.auth import get_user_model
from django.utils import timezone

from rest_framework import serializers

from custom.enums import (
    Furniture,
    ShelfType,
    Sofa01Color,
)
from gallery.constants import FURNITURE_COLOR_CHOICES
from gallery.enums import FurnitureCategory
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from mailing.constants import (
    MAILING_VOUCHER_REGIONALIZED_VALUES,
    MAILING_VOUCHER_SAMPLE_LIMITS,
    SampleVoucherValue,
    VoucherValue,
)
from pricing_v3.serializers import PricingSerializer
from regions.models import Region
from vouchers.constants import VOUCHER_PERCENTAGE_LIMIT
from vouchers.enums import (
    ServiceType,
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import (
    ItemDiscount,
    Voucher,
    VoucherGroup,
    VoucherRegionEntry,
)

User = get_user_model()

if typing.TYPE_CHECKING:
    from orders.models import Order


class VoucherSerializer(serializers.ModelSerializer):
    type = serializers.ReadOnlyField(source='get_kind_of_display')

    class Meta:
        model = Voucher
        fields = (
            'type',
            'code',
            'origin',
            'start_date',
            'end_date',
            'value',
            'active',
            'amount_starts',
            'amount_limit',
            'item_conditionals',
            'notes',
            'ignore_on_invoice',
            'group',
            'related_users',
            'discounts',
        )


class VoucherGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = VoucherGroup
        fields = (
            'code',
            'region',
        )


class VoucherRegionEntrySerializer(serializers.ModelSerializer):
    class Meta:
        model = VoucherRegionEntry
        fields = (
            'amount_limit',
            'amount_starts',
            'value',
            'region',
        )


class ItemDiscountSerializer(serializers.ModelSerializer):
    class Meta:
        model = ItemDiscount
        fields = (
            'shelf_type',
            'furniture_type',
            'material',
            'price_order',
            'box_variant',
            'furniture_category',
            'value',
            'kind_of',
        )


class PromoVoucherSerializer(serializers.ModelSerializer):
    group = VoucherGroupSerializer(many=False, allow_null=True, required=False)
    region_entries = VoucherRegionEntrySerializer(
        many=True,
        allow_null=True,
        required=False,
    )
    discounts = ItemDiscountSerializer(
        many=True,
        allow_null=True,
        required=False,
    )

    class Meta:
        model = Voucher
        fields = (
            'group',
            'region_entries',
            'discounts',
            'kind_of',
            'code',
            'origin',
            'start_date',
            'end_date',
            'value',
            'active',
            'quantity',
            'quantity_left',
            'amount_starts',
            'amount_limit',
            'item_conditionals',
            'ignore_on_invoice',
            'creator',
        )


class ItemFiltersSerializer(serializers.Serializer):
    shelf_types = serializers.ListSerializer(
        child=serializers.ChoiceField(choices=ShelfType.choices()),
        source='shelf_type__in',
        required=False,
    )
    furniture_types = serializers.ListSerializer(
        child=serializers.ChoiceField(choices=Furniture.choices()),
        source='furniture_type__in',
        required=False,
    )
    materials = serializers.ListSerializer(
        child=serializers.ChoiceField(choices=FURNITURE_COLOR_CHOICES),
        source='material__in',
        required=False,
    )


class VoucherItemConditionalsSerializer(ItemFiltersSerializer):
    include = ItemFiltersSerializer(required=False, many=True)
    exclude = ItemFiltersSerializer(required=False, many=True)


class FurnitureMockMixin:
    Meta: ClassVar[type[serializers.SerializerMetaclass]]

    def create(self, validated_data):
        """Only create model instance without committing to database."""
        ModelClass = self.Meta.model
        return ModelClass(**validated_data)


class ShelvingFurnitureMockSerializer(FurnitureMockMixin, serializers.ModelSerializer):
    shelf_category = serializers.ChoiceField(
        choices=FurnitureCategory.choices,
        required=False,
    )

    class Meta:
        fields = ['shelf_category']


class PromoMockJettySerializer(ShelvingFurnitureMockSerializer):
    material = serializers.ChoiceField(
        choices={
            material
            for shelf_type in ShelfType.get_jetty_shelf_types()
            for material in ShelfType(shelf_type).colors.values()
        },
    )
    shelf_type = serializers.ChoiceField(
        choices=ShelfType.jetty_choices(),
    )

    class Meta:
        model = Jetty
        fields = ShelvingFurnitureMockSerializer.Meta.fields + [
            'material',
            'shelf_type',
        ]


class PromoMockSottySerializer(FurnitureMockMixin, serializers.ModelSerializer):
    material = serializers.ChoiceField(
        choices={
            material
            for shelf_type in ShelfType.get_sotty_shelf_types()
            for material in ShelfType(shelf_type).colors.values()
        }
        | {Sofa01Color.MULTICOLOR.value}
    )

    class Meta:
        model = Sotty
        fields = ['material']

    def validate(self, attrs):
        attrs['materials'] = [attrs.pop('material')]
        return attrs


class PromoMockWattySerializer(ShelvingFurnitureMockSerializer):
    material = serializers.ChoiceField(
        choices={
            material
            for shelf_type in ShelfType.get_watty_shelf_types()
            for material in ShelfType(shelf_type).colors.values()
        },
    )
    shelf_type = serializers.ChoiceField(
        choices=ShelfType.watty_choices(),
    )

    class Meta:
        model = Watty
        fields = ShelvingFurnitureMockSerializer.Meta.fields + [
            'material',
            'shelf_type',
        ]


class CheckVoucherResponseSerializer(serializers.Serializer):
    currency_symbol = serializers.SerializerMethodField()
    order_pricing = PricingSerializer(source='*')

    def get_currency_symbol(self, instance: 'Order') -> str:
        return instance.get_region().get_currency().symbol


class MailingVoucherBaseSerializer(serializers.ModelSerializer):
    """Common part of create-only serializer for mailing Vouchers"""

    prefix = serializers.CharField(write_only=True)
    duration = serializers.IntegerField(write_only=True, required=False)  # in days
    end_date = serializers.DateTimeField(required=False)
    for_email = serializers.EmailField()
    exclude_wardrobes = serializers.BooleanField(
        write_only=True,
        default=False,
    )
    exclude_samples = serializers.BooleanField(
        write_only=True,
        default=False,
    )
    exclude_types = serializers.MultipleChoiceField(
        choices=ShelfType.choices(),
        allow_null=True,
        required=False,
    )
    delivery_discount = serializers.DecimalField(
        required=False, allow_null=True, decimal_places=2, max_digits=5
    )

    class Meta:
        model = Voucher
        base_fields = [
            'prefix',
            'duration',
            'end_date',
            'for_email',
            'exclude_wardrobes',
            'exclude_samples',
            'exclude_types',
            'delivery_discount',
        ]
        fields = base_fields + [
            'amount',
        ]

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if 'duration' not in attrs and 'end_date' not in attrs:
            raise serializers.ValidationError(
                'You need to pass "duration" or "end_date" parameter'
            )
        return attrs

    def to_representation(self, instance):
        return {
            'Promocode': instance.code,
            'end_date': instance.end_date.strftime('%d.%m.%Y'),
            'days_left': (instance.end_date - timezone.now()).days,
        }

    def create(self, validated_data):
        params = self._get_params(validated_data)
        voucher = Voucher.objects.filter(**params).order_by('end_date').first()
        if not voucher:
            voucher = self._create_voucher(validated_data)
        self._apply_excludes(voucher, validated_data)
        if delivery_discount := validated_data.get('delivery_discount'):
            self._apply_delivery_discount(voucher, delivery_discount)
        return voucher

    @staticmethod
    def _generate_code(prefix):
        return Voucher.generate_code(
            inside_string=prefix,
            inside_string_at_beginning=True,
            character_count=10,
        )

    def _get_user(self):
        if request := self.context.get('request'):
            return request.user
        return User.objects.get(username='admin')

    @staticmethod
    def _get_end_date(validated_data):
        if 'duration' in validated_data:
            return timezone.now() + timedelta(days=validated_data['duration'])
        elif 'end_date' in validated_data:
            return validated_data['end_date']

    @staticmethod
    def _apply_delivery_discount(voucher: Voucher, delivery_discount: Decimal) -> None:
        item_discount, _ = ItemDiscount.objects.get_or_create(
            value=delivery_discount, service_type=ServiceType.DELIVERY
        )
        item_discount.voucher_set.add(voucher)

    @staticmethod
    def _apply_excludes(voucher, validated_data):
        item_conditionals = voucher.item_conditionals or defaultdict(list)
        if exclude_wardrobes := validated_data.get('exclude_wardrobes', False):
            item_conditionals['exclude'].append({'shelf_types': [ShelfType.TYPE03]})
        if exclude_samples := validated_data.get('exclude_samples', False):
            item_conditionals['exclude'].append({'furniture_types': ['sample_box']})
        if types_to_exclude := validated_data.get('exclude_types', None):
            # MultipleChoiceField returns a set, we need a list
            item_conditionals['exclude'].append({'shelf_types': list(types_to_exclude)})

        if any(
            [
                exclude_wardrobes,
                exclude_samples,
                types_to_exclude,
            ]
        ):
            voucher.item_conditionals = item_conditionals
            voucher.save(update_fields=['item_conditionals'])

    def _get_params(self, validated_data):
        return {
            'origin': VoucherOrigin.MAILING,
            'quantity': 1,
            'quantity_left': 1,
            'amount_limit': Decimal('1000000'),
            'for_email': validated_data['for_email'],
            'end_date__gte': (self._get_end_date(validated_data)).date(),
            'code__startswith': validated_data['prefix'],
            **self.extra_params(validated_data),
        }

    def _create_params(self, validated_data):
        return {
            'origin': VoucherOrigin.MAILING,
            'quantity': 1,
            'quantity_left': 1,
            'amount_limit': Decimal('1000000'),
            'for_email': validated_data['for_email'],
            'start_date': timezone.now(),
            'creator': self._get_user(),
            'end_date': self._get_end_date(validated_data),
            'code': self._generate_code(validated_data['prefix']),
            **self.extra_params(validated_data),
        }

    @staticmethod
    def extra_params(validated_data):
        raise NotImplementedError

    def _create_voucher(self, validated_data):
        create_params = self._create_params(validated_data)
        return Voucher.objects.create(**create_params)


class MailingAbsoluteVoucherSerializer(MailingVoucherBaseSerializer):
    AVAILABLE_PREFIXES = MAILING_VOUCHER_REGIONALIZED_VALUES

    class Meta(MailingVoucherBaseSerializer.Meta):
        fields = MailingVoucherBaseSerializer.Meta.base_fields

    def validate_prefix(self, value):
        if value not in self.AVAILABLE_PREFIXES.keys():
            error_name = 'Invalid voucher configuration'
            raise serializers.ValidationError(
                {error_name: 'Invalid prefix for predefined voucher.'}
            )
        return value

    def extra_params(self, validated_data):
        default_values = self.AVAILABLE_PREFIXES[validated_data['prefix']]['EUR']
        return {
            'amount_starts': default_values.amount_starts,
            'kind_of': VoucherType.ABSOLUTE,
            'value': default_values.value,
        }

    def _create_voucher(self, validated_data):
        voucher = super()._create_voucher(validated_data)
        self._create_region_entries_for_voucher(voucher, validated_data['prefix'])
        return voucher

    def _create_region_entries_for_voucher(self, voucher, prefix):
        regionalized_values = self.AVAILABLE_PREFIXES[prefix]
        voucher.region_entries.all().delete()
        region_entries = []
        for region in Region.objects.select_related('currency').all():
            region_voucher_data = regionalized_values.get(region.currency.code)
            if region_voucher_data:
                region_entries.append(
                    self._get_region_entry(
                        voucher_id=voucher.id,
                        region_id=region.id,
                        voucher_data=region_voucher_data,
                    )
                )
        VoucherRegionEntry.objects.bulk_create(region_entries)

    def _get_region_entry(
        self,
        voucher_id: int,
        region_id: int,
        voucher_data: VoucherValue,
    ) -> VoucherRegionEntry:
        return VoucherRegionEntry(
            voucher_id=voucher_id,
            region_id=region_id,
            value=voucher_data.value,
            amount_starts=voucher_data.amount_starts,
            amount_limit=Decimal(1000000),
        )


class MailingPercentageVoucherSerializer(MailingVoucherBaseSerializer):
    amount = serializers.IntegerField()
    item_discounts = serializers.PrimaryKeyRelatedField(
        queryset=ItemDiscount.objects.all(), many=True, required=False
    )

    class Meta(MailingVoucherBaseSerializer.Meta):
        fields = MailingVoucherBaseSerializer.Meta.fields + ['item_discounts']

    def to_representation(self, instance):
        return {
            **super().to_representation(instance),
            'value': f'{instance.value}%',
        }

    @staticmethod
    def validate_amount(value):
        if value > VOUCHER_PERCENTAGE_LIMIT:
            error_name = 'The value of the voucher exceeded the limit'
            raise serializers.ValidationError(
                {
                    error_name: f'The value of the voucher is {value} and '
                    f'it exceeded the limit of {VOUCHER_PERCENTAGE_LIMIT}%'
                    ' for percentage vouchers.'
                }
            )
        return value

    @staticmethod
    def extra_params(validated_data):
        return {
            'amount_starts': 0,
            'kind_of': VoucherType.PERCENTAGE,
            'value': validated_data['amount'],
        }

    def _create_voucher(self, validated_data):
        voucher = super()._create_voucher(validated_data)
        voucher.discounts.set(validated_data.get('item_discounts', []))
        return voucher

    def _get_params(self, validated_data):
        params = super()._get_params(validated_data)
        item_discounts = validated_data.get('item_discounts', [])
        if item_discounts:
            params['discounts__in'] = item_discounts
        else:
            params['discounts__isnull'] = True
        return params


class MailingSampleSerializer(MailingAbsoluteVoucherSerializer):
    """Used to create sample vouchers. There are always percentage and differ by
    discount value and item limit.
    """

    AVAILABLE_PREFIXES = MAILING_VOUCHER_SAMPLE_LIMITS

    class Meta(MailingVoucherBaseSerializer.Meta):
        fields = [
            'prefix',
            'duration',
            'end_date',
            'for_email',
        ]

    def extra_params(self, validated_data: dict) -> dict:
        default_values = self.AVAILABLE_PREFIXES[validated_data['prefix']]['EUR']
        return {
            'amount_limit': default_values.limit,
            'kind_of': VoucherType.PERCENTAGE,
            'value': default_values.value,
        }

    def _get_region_entry(
        self,
        voucher_id: int,
        region_id: int,
        voucher_data: SampleVoucherValue,
    ) -> VoucherRegionEntry:
        return VoucherRegionEntry(
            voucher_id=voucher_id,
            region_id=region_id,
            value=voucher_data.value,
            amount_starts=Decimal(0),
            amount_limit=voucher_data.limit,
        )

    @staticmethod
    def _apply_excludes(voucher: Voucher, validated_data: dict) -> None:
        # always exclude all furniture
        voucher.item_conditionals = {
            'exclude': [
                {'furniture_types': [Furniture.jetty.value, Furniture.watty.value]}
            ]
        }
        voucher.save(update_fields=['item_conditionals'])
