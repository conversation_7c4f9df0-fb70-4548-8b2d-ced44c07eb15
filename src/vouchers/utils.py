from datetime import date
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    TYPE_CHECKING,
    Union,
)

from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils import timezone

from dateutil.relativedelta import relativedelta

from custom.enums import ShelfType
from regions.models import Region
from vouchers.constants import VOUCHER_B2B_LIMIT
from vouchers.enums import (
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import (
    ItemDiscount,
    Voucher,
    VoucherRegionEntry,
    VoucherSettings,
)

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order


User = get_user_model()


def create_free_sample_b2b_voucher(email: str) -> Voucher:
    code = Voucher.generate_code(
        inside_string='MSD',
        inside_string_at_beginning=True,
        character_count=8,
    )
    return Voucher.objects.create(
        kind_of=VoucherType.PERCENTAGE,
        code=code,
        value=Decimal(100),
        quantity=10,
        quantity_left=10,
        start_date=date.today(),
        end_date=timezone.now() + relativedelta(years=1),
        amount_starts=Decimal(0),
        amount_limit=Decimal(80),
        creator=User.objects.get(username='admin'),
        for_email=email,
        origin=VoucherOrigin.B2B,
        item_conditionals={'exclude': [{'shelf_types': ShelfType.values()}]},
    )


def create_b2b_vouchers(quantity: int, for_email: str | None = None) -> list[str]:
    params = _get_params_for_b2b_voucher(for_email=for_email)
    codes = []
    vouchers = []
    n = quantity
    while n > 0:
        code = Voucher.generate_code(
            inside_string='B2B',
            inside_string_at_beginning=True,
            character_count=8,
        )
        if code not in codes:
            codes.append(code)
            vouchers.append(Voucher(code=code, **params))
            n -= 1
    vouchers_before_discount = Voucher.objects.bulk_create(vouchers)

    # prevent vouchers from being used to buy sample boxes
    discount_for_samples_excluded, _ = ItemDiscount.objects.get_or_create(
        furniture_type='sample_box',
        value=0,
    )
    if discount_for_samples_excluded:
        discount_for_samples_excluded.voucher_set.add(*vouchers_before_discount)

    return codes


def _get_params_for_b2b_voucher(for_email: str | None = None) -> dict:
    start_date = date.today()
    params = {
        'kind_of': VoucherType.PERCENTAGE,
        'quantity': VOUCHER_B2B_LIMIT,
        'quantity_left': VOUCHER_B2B_LIMIT,
        'creator': User.objects.get(username='admin'),
        'origin': VoucherOrigin.B2B,
        'start_date': start_date,
        'end_date': start_date + relativedelta(years=1),
        'value': VoucherSettings.b2b_voucher_percentage_value(),
        'amount_starts': Decimal('0'),
        'amount_limit': Decimal('1000000'),
    }
    if for_email:
        params.update({'for_email': for_email})

    return params


def get_or_create_extra_discount_voucher(
    voucher_value: Decimal,
    kind_of: 'VoucherType',
) -> 'Voucher':
    voucher, created = Voucher.objects.get_or_create(
        kind_of=kind_of,
        code=f'extra_discount_{voucher_value}',
        creator=User.objects.get(username='admin'),
        origin=VoucherOrigin.CUSTOMER_SUPPORT,
        value=Decimal(voucher_value),
        active=True,
        defaults={
            'start_date': timezone.now(),
            'quantity': 32000,
            'quantity_left': 32000,
            'amount_starts': Decimal('1'),
            'amount_limit': Decimal('100000'),
        },
    )
    return voucher


def convert_region_to_euro(value: Decimal, region: 'Region') -> Decimal:
    euro_value = value / region.get_currency().current_rate.rate
    return euro_value.quantize(Decimal('1'), rounding=ROUND_HALF_UP)


def get_or_create_klarna_absolute_discount_voucher(
    region: 'Region',
    region_voucher_value: Decimal,
) -> 'Voucher':

    euro_voucher_value = convert_region_to_euro(region_voucher_value, region)
    voucher, created = Voucher.objects.get_or_create(
        kind_of=VoucherType.ABSOLUTE,
        code=f'klarna_{euro_voucher_value}',
        creator=User.objects.get(username='admin'),
        origin=VoucherOrigin.CUSTOMER_SUPPORT,
        value=euro_voucher_value,
        active=True,
        defaults={
            'start_date': timezone.now(),
            'quantity': 32000,
            'quantity_left': 32000,
            'amount_starts': Decimal('1'),
            'amount_limit': Decimal('100000'),
        },
    )
    VoucherRegionEntry.objects.get_or_create(
        voucher=voucher,
        region=region,
        value=region_voucher_value,
        defaults={
            'amount_starts': Decimal('1'),
            'amount_limit': Decimal('100000'),
        },
    )
    return voucher


def convert_absolute_voucher_to_percentage(instance: Union['Cart', 'Order']) -> Decimal:
    before_promo = instance.region_total_price + instance.region_promo_amount
    if instance.assembly:
        total_region_assembly_price = instance.get_assembly_price()
        before_promo -= total_region_assembly_price

    if before_promo != 0:
        return instance.region_promo_amount / before_promo
    return Decimal('1')


def update_b2b_percentage_vouchers(value: Decimal) -> None:
    b2b_vouchers = Voucher.objects.filter(
        active=True,
        origin=VoucherOrigin.B2B,
        kind_of=VoucherType.PERCENTAGE,
        quantity_left__gt=0,
    ).filter(
        Q(code__istartswith='ksb2b') | Q(code__istartswith='b2b'),
    )

    b2b_vouchers.update(value=value)
