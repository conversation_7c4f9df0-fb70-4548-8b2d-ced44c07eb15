# Generated by Django 3.2.16 on 2023-02-13 13:27
import logging

from decimal import Decimal

from django.db import migrations

logger = logging.getLogger('cstm')


def update_chf_rate(apps, schema_editor):
    Currency = apps.get_model('regions', 'Currency')
    CurrencyRate = apps.get_model('regions', 'CurrencyRate')
    try:
        currency = Currency.objects.get(code='CHF')
        CurrencyRate.objects.create(
            currency=currency,
            rate=Decimal(1),
        )
    except Currency.DoesNotExist as e:
        logger.error(e, exc_info=True)


class Migration(migrations.Migration):

    dependencies = [
        ('regions', '0005_add_sku_and_fast_track_to_region'),
    ]

    operations = [
        migrations.RunPython(
            update_chf_rate,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
