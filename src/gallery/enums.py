import enum

from typing import Any

from django.db import models
from django.utils.translation import gettext as _

import unidecode

from custom.enums import (
    ChoicesMixin,
    Furniture,
    ShelfType,
)


class ShelfStatusSource(models.TextChoices):
    SAVE_FOR_LATER = 'save_for_later'
    ADD_TO_CART = 'add_to_cart'
    PRODUCT_VIEW = 'view'
    TRANSACTION = 'transaction'


class DnaVersion(ChoicesMixin, enum.IntEnum):
    """Describes the version od the dna.

    It is mainly used for mobile app. Each time we release a major dna change,
    it comes with the updated FPM as well. Since we can't update FPM for older versions
    of the app, we use dna versioning to differentiate dna so that older versions of
    app gets older dnas and vice versa.

    Note: Leaving ``BOTH`` as 0 since it's a special case
    """

    BOTH = 0
    VERSION1 = 1
    VERSION2 = 2
    VERSION3 = 3
    VERSION4 = 4
    VERSION5 = 5
    VERSION6 = 6


class FurnitureStatusEnum(models.IntegerChoices):
    DRAFT = 0, 'draft'
    SAVED = 1, 'saved'
    ORDERED = 2, 'ordered'
    SHARED = 3, 'shared'
    SPECIAL = 4, 'special'


class ShelfPatternEnum(models.IntegerChoices):
    SLANT = 0, 'Slant'
    GRADIENT = 1, 'Gradient'
    PATTERN = 2, 'Pattern'
    GRID = 3, 'Grid'
    FRAME = 4, 'Frame'
    PIXEL = 5, 'Pixel'
    MOSAIC = 6, 'Mosaic'

    @classmethod
    def _missing_(cls, value: object) -> Any:
        return cls.SLANT

    @classmethod
    def get_value_by_label(cls, label: str) -> int:
        for member in cls:
            if member.value[1] == label:
                return member.value[0]

        return cls.SLANT

    @classmethod
    def get_new_patterns(cls) -> set:
        return set()


class CapeCollectionType(models.TextChoices):
    SIDEBOARD = 'Sideboard'
    SHOE_RACK = 'ShoeRack'
    WARDROBE = 'Wardrobe'
    CHEST = 'Chest'
    BOOKCASE = 'Bookcase'
    VINYL_STORAGE = 'VinylStorage'
    DESK = 'Desk'
    CABINET = 'Cabinet'
    WALL_STORAGE = 'Wallstorage'
    SOFA = 'Sofa'

    def get_watty_category_from_collection_type(self) -> 'FurnitureCategory':
        category_map = {
            self.WALL_STORAGE: FurnitureCategory.WALL_STORAGE,
            self.CABINET: FurnitureCategory.WARDROBE,
            self.WARDROBE: FurnitureCategory.WARDROBE,
            self.BOOKCASE: FurnitureCategory.BOOKCASE,
            self.CHEST: FurnitureCategory.CHEST,
        }
        return category_map.get(self, FurnitureCategory.WARDROBE)

    def get_configurator_type(self, shelf_type: ShelfType) -> 'ConfiguratorTypeEnum':
        configurator_types = {
            CapeCollectionType.SIDEBOARD: {
                Furniture.jetty: ConfiguratorTypeEnum.COLUMN,
                Furniture.watty: ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            },
            CapeCollectionType.SHOE_RACK: {
                Furniture.jetty: ConfiguratorTypeEnum.COLUMN,
            },
            CapeCollectionType.WARDROBE: {
                Furniture.watty: ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            },
            CapeCollectionType.CHEST: {
                Furniture.watty: ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            },
            CapeCollectionType.VINYL_STORAGE: {
                Furniture.jetty: ConfiguratorTypeEnum.ROW,
            },
            CapeCollectionType.DESK: {
                Furniture.jetty: ConfiguratorTypeEnum.COLUMN,
            },
            CapeCollectionType.CABINET: {
                Furniture.watty: ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            },
            CapeCollectionType.BOOKCASE: {
                Furniture.jetty: ConfiguratorTypeEnum.ROW,
                Furniture.watty: ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            },
            CapeCollectionType.WALL_STORAGE: {
                Furniture.jetty: ConfiguratorTypeEnum.ROW,
                Furniture.watty: ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            },
            CapeCollectionType.SOFA: {
                Furniture.sotty: ConfiguratorTypeEnum.SOFA,
            },
        }
        return configurator_types.get(self, {}).get(shelf_type.furniture_type)


class ConfiguratorTypeEnum(ChoicesMixin, enum.IntEnum):
    ROW = 1
    COLUMN = 2
    MIXED_ROW_COLUMN = 3
    SOFA = 4

    @classmethod
    def sotty_choices(cls):
        choices = [
            cls.SOFA,
        ]
        return [(member.value, member.name) for member in choices]


class FurnitureImageType(models.TextChoices):
    FEED_IMAGE = 'feed'
    INSTA_GRID_IMAGE = 'insta_grid'
    S4L_RENDER = 's4l_render'
    S4L_UNREAL_SCENE = 's4l_unreal_scene'
    S4L_UNREAL_BLENDER = 's4l_unreal_blender'
    UNREAL_STUDIO = 'unreal_studio'
    UNREAL_CONFIGURATOR_PREVIEW = 'unreal_configurator_preview'
    UNREAL_GALLERY_DESKTOP = 'unreal_gallery_desktop'
    UNREAL_GALLERY_MOBILE = 'unreal_gallery_mobile'
    RENDER = 'render'
    RENDER_SMALL = 'render_small'
    HOVER_RENDER = 'hover_render'
    HOVER_RENDER_SMALL = 'hover_render_small'
    GRID_LARGE = 'grid_large'
    TECHNICAL_SKETCH = 'technical_sketch'


class FurnitureCategory(models.TextChoices):
    VINYL_STORAGE = 'vinyl_storage'
    VINYL_STORAGE_FE = 'vinylstorage'  # todo Remove this after thorough testing
    CHEST = 'chest'
    SHOERACK = 'shoerack'
    TV_STAND = 'tvstand'
    SIDEBOARD = 'sideboard'
    BOOKCASE = 'bookcase'
    WALL_STORAGE = 'wallstorage'
    WARDROBE = 'wardrobe'
    BEDSIDE_TABLE = 'bedside_table'
    DESK = 'desk'
    DRESSING_TABLE = 'dressing_table'
    TWO_SEATER = 'two_seater'
    THREE_SEATER = 'three_seater'
    FOUR_PLUS_SEATER = 'four_plus_seater'
    CORNER = 'corner'
    CHAISE_LONGUE = 'chaise_longue'
    ARMCHAIR = 'armchair'
    FOOTREST_AND_MODULES = 'footrests_and_modules'
    COVER = 'cover'

    @classmethod
    def jetty_choices(cls):
        choices = [
            cls.VINYL_STORAGE,
            cls.CHEST,
            cls.SHOERACK,
            cls.TV_STAND,
            cls.SIDEBOARD,
            cls.BOOKCASE,
            cls.WALL_STORAGE,
            cls.BEDSIDE_TABLE,
            cls.DESK,
            cls.DRESSING_TABLE,
        ]
        return [(member.value, member.name) for member in choices]

    @classmethod
    def get_desk_like_choices(cls):
        choices = [
            cls.DESK,
            cls.DRESSING_TABLE,
        ]
        return [(member.value, member.name) for member in choices]

    @classmethod
    def watty_choices(cls):
        choices = [
            cls.CHEST,
            cls.BOOKCASE,
            cls.WALL_STORAGE,
            cls.WARDROBE,
            cls.TV_STAND,
            cls.SIDEBOARD,
            cls.BEDSIDE_TABLE,
        ]
        return [(member.value, member.name) for member in choices]

    @classmethod
    def sotty_choices(cls):
        choices = [
            cls.TWO_SEATER,
            cls.THREE_SEATER,
            cls.FOUR_PLUS_SEATER,
            cls.CORNER,
            cls.CHAISE_LONGUE,
            cls.ARMCHAIR,
            cls.FOOTREST_AND_MODULES,
            cls.COVER,
        ]
        return [(member.value, member.name) for member in choices]

    @classmethod
    def get_shelving(cls) -> list['FurnitureCategory']:
        return [
            cls.VINYL_STORAGE,
            cls.CHEST,
            cls.SHOERACK,
            cls.TV_STAND,
            cls.SIDEBOARD,
            cls.BOOKCASE,
            cls.WALL_STORAGE,
            cls.BEDSIDE_TABLE,
            cls.WARDROBE,
            cls.DESK,
            cls.DRESSING_TABLE,
        ]

    @classmethod
    def _missing_(cls, key):
        return cls.BOOKCASE

    @property
    def canonical_id(self):
        furniture_canonical_ids = {
            self.VINYL_STORAGE: 335079,
            self.VINYL_STORAGE_FE: 335079,
            self.CHEST: 273393,
            self.SHOERACK: 273396,
            self.TV_STAND: 273395,
            self.SIDEBOARD: 317407,
            self.BOOKCASE: 317247,
            self.WALL_STORAGE: 273390,
            self.WARDROBE: 148,
            self.BEDSIDE_TABLE: 3518145,
            self.DESK: 3716770,
        }
        return furniture_canonical_ids.get(self)

    @property
    def translated_name(self):
        return self._get_translations().get(self)['singular']

    @property
    def translated_name_plural(self):
        return self._get_translations().get(self)['plural']

    @property
    def unidecoded_translated_name(self):
        return self._unidecode_translate(self.translated_name)

    @property
    def unidecoded_translated_name_plural(self):
        return self._unidecode_translate(self.translated_name_plural)

    def get_seo_data(self) -> dict:
        seo_data = {
            self.VINYL_STORAGE.value: {
                'title': _('SEO_CATEGORY_Vinyl_Title'),
                'description': _('SEO_CATEGORY_Vinyl_Meta'),
            },
            self.CHEST.value: {
                'title': _('SEO_CATEGORY_Chest_Title'),
                'description': _('SEO_CATEGORY_Chest_Meta'),
            },
            self.SHOERACK.value: {
                'title': _('SEO_CATEGORY_Shoe_Racks_Title'),
                'description': _('SEO_CATEGORY_Shoe_Racks_Meta'),
            },
            self.TV_STAND.value: {
                'title': _('SEO_CATEGORY_TV_Stands_Title'),
                'description': _('SEO_CATEGORY_TV_Stands_Meta'),
            },
            self.SIDEBOARD.value: {
                'title': _('SEO_CATEGORY_Sideboards_Title'),
                'description': _('SEO_CATEGORY_Sideboards_Meta'),
            },
            self.BOOKCASE.value: {
                'title': _('SEO_CATEGORY_Bookcases_Title'),
                'description': _('SEO_CATEGORY_Bookcases_Meta'),
            },
            self.WALL_STORAGE.value: {
                'title': _('SEO_CATEGORY_Wall_Storage_Title'),
                'description': _('SEO_CATEGORY_Wall_Storage_Meta'),
            },
            self.WARDROBE.value: {
                'title': _('SEO_CATEGORY_Wardrobe_Title'),
                'description': _('SEO_CATEGORY_Wardrobe_Meta'),
            },
            self.BEDSIDE_TABLE.value: {
                'title': _('SEO_CATEGORY_Bedside_Table_Title'),
                'description': _('SEO_CATEGORY_Bedside_Table_Meta'),
            },
            self.DESK.value: {
                'title': _('SEO_CATEGORY_Desk_Title'),
                'description': _('SEO_CATEGORY_Desk_Meta'),
            },
            self.TWO_SEATER.value: {
                'title': _('SEO_CATEGORY_Two_Seater_Title'),
                'description': _('SEO_CATEGORY_Two_Seater_Meta'),
            },
            self.THREE_SEATER.value: {
                'title': _('SEO_CATEGORY_Three_Seater_Title'),
                'description': _('SEO_CATEGORY_Three_Seater_Meta'),
            },
            self.FOUR_PLUS_SEATER.value: {
                'title': _('SEO_CATEGORY_Four_plus_Seater_Title'),
                'description': _('SEO_CATEGORY_Four_plus_Seater_Meta'),
            },
            self.CORNER.value: {
                'title': _('SEO_CATEGORY_Corner_Title'),
                'description': _('SEO_CATEGORY_Corner_Meta'),
            },
            self.CHAISE_LONGUE.value: {
                'title': _('SEO_CATEGORY_Chaise_Longue_Title'),
                'description': _('SEO_CATEGORY_Chaise_Longue_Meta'),
            },
            self.ARMCHAIR.value: {
                'title': _('SEO_CATEGORY_Armchair_Title'),
                'description': _('SEO_CATEGORY_Armchair_Meta'),
            },
            self.FOOTREST_AND_MODULES.value: {
                'title': _('SEO_CATEGORY_Footrest_and_Modules_Title'),
                'description': _('SEO_CATEGORY_Footrest_and_Modules_Meta'),
            },
            self.COVER.value: {
                'title': _('SEO_CATEGORY_Cover_Title'),
                'description': _('SEO_CATEGORY_Cover_Meta'),
            },
            self.DRESSING_TABLE.value: {
                'title': _('SEO_CATEGORY_Dressing_Table_Title'),
                'description': _('SEO_CATEGORY_Dressing_Table_Meta'),
            },
        }
        return seo_data.get(self, {})

    def _get_translations(self) -> dict:
        return {
            self.VINYL_STORAGE: {
                'singular': _('martin_common_vinyl_storage_singular'),
                'plural': _('martin_common_vinyl_storage_plural_nobr'),
            },
            self.VINYL_STORAGE_FE: {
                'singular': _('martin_common_vinyl_storage_singular'),
                'plural': _('martin_common_vinyl_storage_plural'),
            },
            self.CHEST: {
                'singular': _('martin_common_chest_drawers_singular'),
                'plural': _('martin_common_chest_drawers_plural'),
            },
            self.SHOERACK: {
                'singular': _('martin_common_shoe_rack_singular'),
                'plural': _('martin_common_shoe_rack_plural'),
            },
            self.TV_STAND: {
                'singular': _('martin_common_tv_stand_singular'),
                'plural': _('martin_common_tv_stand_plural'),
            },
            self.SIDEBOARD: {
                'singular': _('martin_common_sideboard_singular'),
                'plural': _('martin_common_sideboard_plural'),
            },
            self.BOOKCASE: {
                'singular': _('martin_common_bookcase_singular'),
                'plural': _('martin_common_bookcase_plural'),
            },
            self.WALL_STORAGE: {
                'singular': _('martin_common_wall_storage_singular'),
                'plural': _('martin_common_wall_storage_plural'),
            },
            self.WARDROBE: {
                'singular': _('martin_common_wardrobe_singular'),
                'plural': _('martin_common_wardrobe_plural_nobr'),
            },
            self.BEDSIDE_TABLE: {
                'singular': _('martin_common_bedside_singular'),
                'plural': _('martin_common_bedside_plural'),
            },
            self.DESK: {
                'singular': _('martin_common_desk_singular'),
                'plural': _('martin_common_desk_plural'),
            },
            self.DRESSING_TABLE: {
                'singular': _('martin_common_dressing_table_singular'),
                'plural': _('martin_common_dressing_table_plural'),
            },
            self.TWO_SEATER: {
                'singular': _('martin_common_two_seater_singular'),
                'plural': _('martin_common_two_seater_plural'),
            },
            self.THREE_SEATER: {
                'singular': _('martin_common_three_seater_singular'),
                'plural': _('martin_common_three_seater_plural'),
            },
            self.FOUR_PLUS_SEATER: {
                'singular': _('martin_common_four_plus_seater_singular'),
                'plural': _('martin_common_four_plus_seater_plural'),
            },
            self.CORNER: {
                'singular': _('martin_common_corner_singular'),
                'plural': _('martin_common_corner_plural'),
            },
            self.CHAISE_LONGUE: {
                'singular': _('martin_common_chaise_longue_singular'),
                'plural': _('martin_common_chaise_longue_plural'),
            },
            self.ARMCHAIR: {
                'singular': _('martin_common_armchair_singular'),
                'plural': _('martin_common_armchair_plural'),
            },
            self.FOOTREST_AND_MODULES: {
                'singular': _('martin_common_footrest_and_modules_singular'),
                'plural': _('martin_common_footrest_and_modules_plural'),
            },
            self.COVER: {
                'singular': _('martin_common_cover_singular'),
                'plural': _('martin_common_cover_plural'),
            },
        }

    @property
    def proper_category_configuration(self) -> list[ConfiguratorTypeEnum]:
        proper_category_conf_dict = {
            self.BEDSIDE_TABLE: [
                ConfiguratorTypeEnum.COLUMN,
                ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            ],
            self.BOOKCASE: [
                ConfiguratorTypeEnum.ROW,
                ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            ],
            self.CHEST: [
                ConfiguratorTypeEnum.COLUMN,
                ConfiguratorTypeEnum.ROW,
                ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            ],
            self.DESK: [ConfiguratorTypeEnum.COLUMN],
            self.DRESSING_TABLE: [ConfiguratorTypeEnum.COLUMN],
            self.SHOERACK: [ConfiguratorTypeEnum.COLUMN, ConfiguratorTypeEnum.ROW],
            self.SIDEBOARD: [
                ConfiguratorTypeEnum.COLUMN,
                ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            ],
            self.TV_STAND: [
                ConfiguratorTypeEnum.COLUMN,
                ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            ],
            self.VINYL_STORAGE: [ConfiguratorTypeEnum.ROW],
            self.WALL_STORAGE: [
                ConfiguratorTypeEnum.ROW,
                ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
            ],
            self.WARDROBE: [ConfiguratorTypeEnum.MIXED_ROW_COLUMN],
            self.TWO_SEATER: [ConfiguratorTypeEnum.SOFA],
            self.THREE_SEATER: [ConfiguratorTypeEnum.SOFA],
            self.FOUR_PLUS_SEATER: [ConfiguratorTypeEnum.SOFA],
            self.CHAISE_LONGUE: [ConfiguratorTypeEnum.SOFA],
            self.CORNER: [ConfiguratorTypeEnum.SOFA],
            self.ARMCHAIR: [ConfiguratorTypeEnum.SOFA],
            self.FOOTREST_AND_MODULES: [ConfiguratorTypeEnum.SOFA],
            self.COVER: [ConfiguratorTypeEnum.SOFA],
        }
        return proper_category_conf_dict.get(self)

    @staticmethod
    def _unidecode_translate(translated_name: str) -> str:
        return unidecode.unidecode(translated_name).replace(' ', '-').lower()

    # TODO: Remove the methods below after removing VINYL_STORAGE_FE, which is not
    #  supposed to be used but still needs to be thoroughly tested.

    @classmethod
    def valid_choices(cls):
        return [
            (member.value, member.name.replace('_', ' ').title())
            for member in cls
            if member.value != cls.VINYL_STORAGE_FE.value
        ]

    @classmethod
    def valid_values(cls):
        return [
            member.value for member in cls if member.value != cls.VINYL_STORAGE_FE.value
        ]

    @classmethod
    def get_plp_categories(cls):
        return [category for category in list(cls) if category != cls.VINYL_STORAGE_FE]


class CollectiveFurnitureCategory(models.TextChoices):
    """Not all furniture categories are in those collective categories because
    of business reasons."""

    HIGH_FURNITURE = 'high_furniture'
    LOW_FURNITURE = 'low_furniture'

    def get_furniture_categories(self) -> list[FurnitureCategory]:
        furniture_category_map = {
            self.HIGH_FURNITURE: [
                FurnitureCategory.WALL_STORAGE,
                FurnitureCategory.BOOKCASE,
                FurnitureCategory.VINYL_STORAGE,
            ],
            self.LOW_FURNITURE: [
                FurnitureCategory.SIDEBOARD,
                FurnitureCategory.TV_STAND,
                FurnitureCategory.CHEST,
                FurnitureCategory.BEDSIDE_TABLE,
                FurnitureCategory.SHOERACK,
                FurnitureCategory.VINYL_STORAGE,  # this is doubled on purpose
            ],
        }
        return furniture_category_map.get(self, [])


class SellableItemContentTypes(models.TextChoices):
    JETTY = 'jetty'
    SAMPLE_BOX = 'samplebox'
    SOTTY = 'sotty'
    WATTY = 'watty'

    @classmethod
    def get_furniture_choices(cls) -> set[str]:
        return {
            furniture_type.value
            for furniture_type in cls
            if furniture_type != cls.SAMPLE_BOX
        }


class SottySeatingDepthCases(models.TextChoices):
    SEATERS = 'seaters'
    CORNER = 'single_corner'
    SINGLE_FOOTRESTS = 'single_footrests'
    SINGLE_EXTENDED_CHAISE_LONGUE = 'extended_chaise_longue'
    CHAISE_LONGUE = 'chaise_longue'
