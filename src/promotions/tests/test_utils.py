from custom.enums import ShelfType, Furniture
from gallery.enums import FurnitureCategory
from promotions.utils import get_categories_in_promo
from vouchers.enums import VoucherType


class TestGetCategoriesInPromo:
    """
    1. all categories are discounted
    2. one category is explicitly discounted, other are not
    4. Shelving categories are discounted, sofa is not
    5. Shelving categories are discounted, sofa are discounted for 0%
    """

    def _get_category_promo_from_list(self, input_value: list, category_name: FurnitureCategory) -> dict:
        return next(
            (
                category
                for category in input_value
                if category['categoryName'] == category_name
            ),
            None,
        )
    def test_all_categories_are_discounted(
        self,
        voucher_factory,
        promotion_factory,
        promotion_config_factory,
        region_de,
    ):
        voucher = voucher_factory(
            kind_of=VoucherType.PERCENTAGE,
            value=10,
        )
        promotion = promotion_factory(
            promo_code=voucher,
            active=True,
        )
        promo_config = promotion_config_factory(
            promotion=promotion,
            grid_show_category_promotion=True,
        )
        promo_config.enabled_regions.add(region_de)
        expected_categories = [
            {
                'categoryName': furniture_category,
                'value': '-10%',
            }
            for furniture_category in FurnitureCategory
        ]
        assert get_categories_in_promo(region_de) == expected_categories

    def test_one_category_is_explicitly_discounted(
        self,
        voucher_factory,
        promotion_factory,
        promotion_config_factory,
        item_discount_factory,
        region_de,
    ):
        voucher = voucher_factory(
            kind_of=VoucherType.PERCENTAGE,
            value=10,
        )
        item_discount = item_discount_factory(
            value=20,
            furniture_category=FurnitureCategory.BEDSIDE_TABLE.value,
        )
        voucher.discounts.add(item_discount)

        promotion = promotion_factory(
            promo_code=voucher,
            active=True,
        )
        promo_config = promotion_config_factory(
            promotion=promotion,
            grid_show_category_promotion=True,
        )
        promo_config.enabled_regions.add(region_de)

        function_output = get_categories_in_promo(region_de)
        bedside_discount_from_output = self._get_category_promo_from_list(function_output, FurnitureCategory.BEDSIDE_TABLE)

        assert bedside_discount_from_output['value'] == '-20%'

    def test_shelving_categories_are_discounted_sofa_is_not(
        self,
        voucher_factory,
        item_discount_factory,
        promotion_factory,
        promotion_config_factory,
        region_de,
    ):
        voucher = voucher_factory(
            kind_of=VoucherType.PERCENTAGE,
            value=10,
        )
        item_discount = item_discount_factory(
            value=0,
            shelf_type=ShelfType.SOFA_TYPE01,
        )
        voucher.discounts.add(item_discount)

        promotion = promotion_factory(
            promo_code=voucher,
            active=True,
        )
        promo_config = promotion_config_factory(
            promotion=promotion,
            grid_show_category_promotion=True,
        )
        promo_config.enabled_regions.add(region_de)

        function_output = get_categories_in_promo(region_de)
        two_seater_discount_from_output = self._get_category_promo_from_list(function_output, FurnitureCategory.TWO_SEATER)
        assert two_seater_discount_from_output is None
        bedside_discount_from_output = self._get_category_promo_from_list(function_output, FurnitureCategory.BEDSIDE_TABLE)
        assert bedside_discount_from_output is not None

    def test_shelving_categories_are_discounted_sofa_is_as_well_but_different_discount(
        self,
        voucher_factory,
        item_discount_factory,
        promotion_factory,
        promotion_config_factory,
        region_de,
    ):
        voucher = voucher_factory(
            kind_of=VoucherType.PERCENTAGE,
            value=40,
        )
        item_discount = item_discount_factory(
            value=10,
            shelf_type=ShelfType.SOFA_TYPE01,
        )
        voucher.discounts.add(item_discount)

        promotion = promotion_factory(
            promo_code=voucher,
            active=True,
        )
        promo_config = promotion_config_factory(
            promotion=promotion,
            grid_show_category_promotion=True,
        )
        promo_config.enabled_regions.add(region_de)

        function_output = get_categories_in_promo(region_de)
        two_seater_discount_from_output = self._get_category_promo_from_list(function_output, FurnitureCategory.TWO_SEATER)
        assert two_seater_discount_from_output.get('value')  == '-10%'
        bedside_discount_from_output = self._get_category_promo_from_list(function_output, FurnitureCategory.BEDSIDE_TABLE)
        assert bedside_discount_from_output.get('value') == '-40%'

    def test_shelving_categories_are_discounted_sofa_is_as_well_but_different_discount_by_furniture_type(
        self,
        voucher_factory,
        item_discount_factory,
        promotion_factory,
        promotion_config_factory,
        region_de,
    ):
        voucher = voucher_factory(
            kind_of=VoucherType.PERCENTAGE,
            value=40,
        )
        item_discount = item_discount_factory(
            value=10,
            furniture_type=Furniture.sotty.value,
        )
        voucher.discounts.add(item_discount)

        promotion = promotion_factory(
            promo_code=voucher,
            active=True,
        )
        promo_config = promotion_config_factory(
            promotion=promotion,
            grid_show_category_promotion=True,
        )
        promo_config.enabled_regions.add(region_de)

        function_output = get_categories_in_promo(region_de)
        two_seater_discount_from_output = self._get_category_promo_from_list(
            function_output, FurnitureCategory.TWO_SEATER)
        assert two_seater_discount_from_output.get('value') == '-10%'
        bedside_discount_from_output = self._get_category_promo_from_list(
            function_output,
            FurnitureCategory.BEDSIDE_TABLE,
        )
        assert bedside_discount_from_output.get('value') == '-40%'
