from dataclasses import dataclass
from typing import (
    TYPE_CHECKING,
    Union,
)

from django.conf import settings
from django.db.models import (
    Q,
    QuerySet,
)
from django.utils import timezone

from custom.utils.in_memory_cache import expiring_lru_cache
from gallery.enums import FurnitureCategory
from promotions.decorators import cached_promo
from promotions.models import (
    Promotion,
    PromotionConfig,
)
from regions.models import Region
from regions.types import RegionLikeObject
from vouchers.enums import VoucherType
from vouchers.models import Voucher

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order


def strikethrough_promo_value(
    region: RegionLikeObject | None = None,
) -> float:
    if active_promotion := strikethrough_promo(region=region):
        return active_promotion.promo_code.value
    return 0


@cached_promo()
def strikethrough_promo(
    region: RegionLikeObject | None = None,
) -> Promotion | None:
    now = timezone.now()
    query = Q(
        strikethrough_pricing=True,
        active=True,
        start_date__lte=now,
        end_date__gte=now,
    )
    if region:
        query &= Q(configs__enabled_regions__id=region.id)
    active_promotion = (
        Promotion.objects.filter(query)
        .select_related('promo_code')
        .prefetch_related('promo_code__discounts')
        .distinct()
        .first()
    )
    # check if promotion is setup correctly - has suitable promo code assigned
    if (
        active_promotion
        and active_promotion.promo_code
        and active_promotion.promo_code.kind_of == VoucherType.PERCENTAGE
    ):
        return active_promotion
    return None


@dataclass
class StrikethroughPromoData:
    strikethrough_promo: Promotion | None
    strikethrough_voucher: Voucher | None
    has_strikethrough_promo_applied: bool


def get_strikethrough_promo_data(
    instance: Union['Cart', 'Order']
) -> StrikethroughPromoData:
    strikethrough_promotion = strikethrough_promo(instance.region if instance else None)
    has_strikethrough_promo_applied = False
    strikethrough_voucher = None

    if strikethrough_promotion:
        strikethrough_voucher = strikethrough_promotion.promo_code

        if instance and strikethrough_voucher == instance.used_promo:
            has_strikethrough_promo_applied = True

    return StrikethroughPromoData(
        strikethrough_promotion,
        strikethrough_voucher,
        has_strikethrough_promo_applied,
    )


@expiring_lru_cache(ttl=settings.REGIONS_CACHE_TTL_SECONDS)
def get_categories_in_promo(region: Region | None = None) -> list:
    promo = get_active_promotion(region=region)
    if not promo or not promo.configs.last().grid_show_category_promotion:
        return []

    # Get all discounts (including those with empty furniture_category)
    all_discounts = promo.promo_code.discounts.all()

    # Create a mapping of categories to their specific discount values
    category_discounts = {}
    excluded_categories = set()

    # Process explicit furniture category discounts
    for discount in all_discounts.exclude(furniture_category=''):
        category = FurnitureCategory(discount.furniture_category)
        if discount.value == 0:
            excluded_categories.add(category)
        else:
            category_discounts[category] = discount.value

    # Process shelf type and furniture type discounts
    for discount in all_discounts.filter(furniture_category=''):
        affected_categories = set()

        # Handle shelf type discounts
        if discount.shelf_type is not None:
            from custom.enums import ShelfType
            if discount.shelf_type == ShelfType.SOFA_TYPE01:
                # SOFA_TYPE01 maps to all sofa categories
                affected_categories.update([
                    FurnitureCategory.TWO_SEATER,
                    FurnitureCategory.THREE_SEATER,
                    FurnitureCategory.FOUR_PLUS_SEATER,
                    FurnitureCategory.CORNER,
                    FurnitureCategory.CHAISE_LONGUE,
                    FurnitureCategory.ARMCHAIR,
                    FurnitureCategory.FOOTREST_AND_MODULES,
                    FurnitureCategory.COVER,
                ])

        # Handle furniture type discounts
        if discount.furniture_type:
            from custom.enums import Furniture
            if discount.furniture_type == Furniture.sotty.value:
                # sotty furniture type maps to all sofa categories
                affected_categories.update([
                    FurnitureCategory.TWO_SEATER,
                    FurnitureCategory.THREE_SEATER,
                    FurnitureCategory.FOUR_PLUS_SEATER,
                    FurnitureCategory.CORNER,
                    FurnitureCategory.CHAISE_LONGUE,
                    FurnitureCategory.ARMCHAIR,
                    FurnitureCategory.FOOTREST_AND_MODULES,
                    FurnitureCategory.COVER,
                ])

        # Apply the discount to affected categories
        for category in affected_categories:
            if discount.value == 0:
                excluded_categories.add(category)
            else:
                category_discounts[category] = discount.value

    # Determine which categories to include
    if category_discounts or excluded_categories:
        # If there are specific discounts or exclusions, include all categories
        # but apply specific discounts where available
        all_categories = set(FurnitureCategory)
        result_categories = []

        for category in all_categories:
            if category in excluded_categories:
                continue  # Skip excluded categories

            if category in category_discounts:
                # Use specific discount value
                discount_value = category_discounts[category]
            else:
                # Use base voucher value
                discount_value = promo.promo_code.value

            result_categories.append({
                'categoryName': category,
                'value': f'-{int(discount_value)}%',
            })

        return result_categories
    else:
        # No specific discounts, apply base discount to all or shelving categories
        if promo.promo_code.excludes_sofas:
            categories = FurnitureCategory.get_shelving()
        else:
            categories = FurnitureCategory

        return [
            {
                'categoryName': furniture_category,
                'value': f'-{int(promo.promo_code.value)}%',
            }
            for furniture_category in categories
        ]


def get_active_promotions(
    region: RegionLikeObject | None = None,
) -> QuerySet[Promotion]:
    now = timezone.now()
    filter_query = Q(active=True, start_date__lte=now, end_date__gte=now)
    if region:
        filter_query &= Q(configs__enabled_regions__id=region.id)

    return (
        Promotion.objects.filter(filter_query)
        .select_related(
            'promo_code',
        )
        .prefetch_related(
            'promo_code__discounts',
            'configs',
            'configs__countdown',
            'configs__copies',
            'configs__grid_picture',
            'configs__enabled_regions',
            'configs__copies__ribbon_lines',
        )
    )


@cached_promo()
def get_active_promotion(
    region: RegionLikeObject | None = None,
) -> Promotion | None:
    return get_active_promotions(region=region).last()


def get_active_promotion_config(
    region: RegionLikeObject | None = None,
) -> PromotionConfig | None:
    if active_promotion := get_active_promotion(region=region):
        return active_promotion.configs.first()


def clear_promo_cache():
    strikethrough_promo.clear_cache()
    get_active_promotion.clear_cache()
