import datetime

from decimal import (
    ROUND_HALF_UP,
    Decimal,
)

from django import forms
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.db.models import (
    Sum,
    Value,
)
from django.db.models.functions import Coalesce
from django.forms import (
    CheckboxSelectMultiple,
    ModelMultipleChoiceField,
    SelectDateWidget,
)
from django.forms.models import ModelChoiceIteratorValue
from django.urls import reverse
from django.utils.html import (
    format_html,
    mark_safe,
)
from django.utils.translation import gettext as _

from crispy_forms.helper import FormHelper
from crispy_forms.layout import Submit
from google.cloud import bigquery
from past.utils import old_div

from complaints.models import DamageData
from custom.enums import (
    LanguageEnum,
    SampleBoxVariantEnum,
    ShelfType,
    Type01Color,
    Type02Color,
    Type03Color,
    Type13Color,
    VeneerType01Color,
)
from custom.models import Countries
from customer_service.enums import (
    KlarnaPriceChangeType,
    KlarnaSource,
)
from customer_service.models import (
    CSCorrectionAddressRequest,
    CSCorrectionRequest,
    KlarnaAdjustment,
)
from free_returns.enums import FreeReturnStatusChoices
from free_returns.models import FreeReturn
from invoice.choices import (
    InvoiceItemType,
    InvoiceStatus,
    VatType,
)
from invoice.enums import (
    InvoiceItemDiscountTag,
    InvoiceItemTag,
)
from invoice.models import (
    Invoice,
    InvoiceItem,
)
from kpi.big_query import export_serializer_to_big_query
from orders.enums import OrderStatus
from orders.models import (
    Order,
    OrderItem,
)
from orders.switch_status import furniture_type_to_model
from producers.choices import ProductStatus
from regions.models import Currency
from user_profile.models import UserProfile
from user_profile.serializers import UserProfileB2BBigQuery
from vouchers.enums import (
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import Voucher
from vouchers.utils import get_or_create_extra_discount_voucher

User = get_user_model()


class CSCorrectionRequestForm(forms.ModelForm):
    tag = forms.ChoiceField(choices=InvoiceItemTag.choices())
    discount_tag = forms.ChoiceField(
        choices=InvoiceItemDiscountTag.choices, required=False
    )

    class Meta(object):
        fields = (
            'correction_amount_gross',
            'correction_context',
            'invoice',
            'tag',
        )
        model = CSCorrectionRequest
        widgets = {'invoice': forms.HiddenInput()}

    def clean_invoice(self):
        data = self.cleaned_data['invoice']
        invoice = Invoice.objects.get(id=data.id)
        correctable_statuses = {
            InvoiceStatus.CORRECTING,
            InvoiceStatus.ENABLED,
            InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
        }
        if (
            invoice.corrected_invoice
            and invoice.corrected_invoice.corrections.filter(id__gt=invoice.id).count()
        ):
            raise forms.ValidationError(
                _('This invoice already has corrections'),
            )
        if invoice.status not in correctable_statuses:
            raise forms.ValidationError(
                _('Invoice with this status cannot be corrected'),
            )
        return data

    def clean_discount_tag(self):
        tag = self.cleaned_data['tag']
        discount_tag = self.cleaned_data['discount_tag']
        if (
            tag == f'{InvoiceItemTag.DISCOUNT_QUALITY_DISSATISFACTION.value}'
            and discount_tag.startswith('-')
        ):
            raise ValidationError('Discount tag must be selected')
        return discount_tag


class CSNeutralizeInvoiceForm(forms.ModelForm):
    tag = forms.ChoiceField(choices=InvoiceItemTag.choices())

    class Meta:
        fields = (
            'correction_context',
            'tag',
            'invoice',
        )
        model = CSCorrectionRequest
        widgets = {'invoice': forms.HiddenInput()}


class DeletedInvoiceItemsField(ModelMultipleChoiceField):
    def label_from_instance(self, obj):
        return (
            f'{obj.item_name} {obj.gross_price}{obj.invoice.currency_symbol} '
            f'[Id ({obj.id}) invoice={obj.invoice.pretty_id} '
            f'item Id ({obj.order_item.id})]'
            f'- {obj.order_item.get_furniture_type()} '
            f'({obj.order_item.get_furniture_id()})'
        )


class CSCorrectionInvoiceItemsRequestForm(CSCorrectionRequestForm):
    tag = forms.ChoiceField(choices=InvoiceItemTag.choices())
    deleted_invoice_items = DeletedInvoiceItemsField(queryset=InvoiceItem.objects.all())

    class Meta(object):
        fields = (
            'correction_context',
            'tag',
            'invoice',
            'deleted_invoice_items',
        )
        model = CSCorrectionRequest
        widgets = {'invoice': forms.HiddenInput()}

    def __init__(self, *args, **kwargs):
        inv = kwargs.pop('inv', None)
        super(CSCorrectionInvoiceItemsRequestForm, self).__init__(*args, **kwargs)
        if inv:
            self.fields['deleted_invoice_items'].queryset = InvoiceItem.objects.filter(
                invoice=inv
            )


class CSCorrectionAddressRequestForm(CSCorrectionRequestForm):
    tag = None
    discount_tag = None

    class Meta:
        fields = (
            'correction_context',
            'invoice',
        )
        model = CSCorrectionRequest
        widgets = {'invoice': forms.HiddenInput()}


class CSCorrectionAddressDataRequestForm(forms.ModelForm):
    class Meta:
        fields = (
            'first_name',
            'last_name',
            'street_address_1',
            'street_address_2',
            'company_name',
            'city',
            'postal_code',
            'country',
            'vat',
        )
        model = CSCorrectionAddressRequest


class CSOrderUpdateForm(forms.ModelForm):
    country = forms.ChoiceField(choices=Countries.as_choices())
    invoice_country = forms.ChoiceField(choices=Countries.as_choices())

    class Meta(object):
        model = Order
        fields = (
            'email',
            'first_name',
            'last_name',
            'street_address_1',
            'street_address_2',
            'city',
            'postal_code',
            'country',
            'company_name',
            'vat',
            'invoice_company_name',
            'invoice_vat',
            'invoice_first_name',
            'invoice_last_name',
            'invoice_email',
            'invoice_street_address_1',
            'invoice_street_address_2',
            'invoice_city',
            'invoice_country',
        )


class MultipleFileInput(forms.ClearableFileInput):
    allow_multiple_selected = True


class MultipleFileField(forms.ImageField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('widget', MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result


class CSCreateDamageForm(forms.ModelForm):
    damaged_packaging = MultipleFileField(
        required=False,
        help_text=format_html(
            _(
                '<small style="color:red">The customer should send photos of damaged'
                ' packages with shipping labels visible</small>'
            )
        ),
    )
    damaged_elements = MultipleFileField(
        required=False,
        help_text=format_html(
            _(
                '<small style="color:red">The customer should send photos of damaged'
                ' elements with visible labels on the elements</small>'
            )
        ),
    )

    field_order = [
        'was_reported_to_delivery_company',
        'damaged_packaging',
        'are_any_elements_of_the_shelf_damaged',
        'damaged_elements',
        'list_of_damaged_elements',
        'are_any_elements_missing',
        'additional_comments',
    ]

    class Meta:
        model = DamageData
        fields = (
            'was_reported_to_delivery_company',
            'are_any_elements_of_the_shelf_damaged',
            'are_any_elements_missing',
            'list_of_damaged_elements',
            'additional_comments',
        )
        widgets = {
            'was_reported_to_delivery_company': forms.RadioSelect,
            'are_any_elements_of_the_shelf_damaged': forms.RadioSelect,
            'are_any_elements_missing': forms.RadioSelect,
        }


class TagSelect(forms.Select):
    template_name = 'widgets/select_tag.html'


class OrderItemWithProductModelMultipleChoiceField(ModelMultipleChoiceField):
    def label_from_instance(self, obj):
        product = obj.product_set.first()

        shelf = f'shelf ({product.id})' if product else ''
        assembly_price = (
            f', assembly price ({obj.get_assembly_price()})'
            if obj.order.assembly
            else ''
        )
        return (
            f'Order item ({obj.id}) - {shelf} {obj.get_furniture_type()} '
            f'({obj.get_furniture_id()}) {assembly_price} x {obj.quantity} '
        )


class CSFreeReturnCreateForm(forms.ModelForm):
    return_assembly_price = forms.DecimalField(
        initial=0, min_value=0, decimal_places=2, step_size=0.01
    )
    order_item = OrderItemWithProductModelMultipleChoiceField(
        queryset=OrderItem.objects.all(),
        required=True,
        help_text='In cases where the quantity is greater than 1 and you want to '
        'create a free return for only a subset of the items, '
        'ask OP4-IT to handle it',
    )

    class Meta:
        model = FreeReturn
        fields = (
            'notification_date',
            'reason',
            'reason_tag',
            'is_packed',
            'is_need_packaging',
            'is_send_asap',
        )
        widgets = {
            'reason': forms.Textarea(attrs={'rows': 3}),
            'reason_tag': TagSelect(),
        }

    def __init__(self, **kwargs):
        self.pk = kwargs.pop('pk')
        order_items = OrderItem.objects.filter(
            order_id=self.pk,
            free_return__isnull=True,
        ).exclude(
            product__status=ProductStatus.ABORTED,
        )
        self.base_fields['order_item'].queryset = order_items
        order = Order.objects.get(pk=self.pk)

        max_possible_assembly_return = self.get_max_possible_assembly_return(
            order, order_items
        )
        super().__init__(**kwargs)
        self.fields['return_assembly_price'].widget.attrs.update(
            {'max': max_possible_assembly_return}
        )
        if not order.assembly:
            self.fields['return_assembly_price'].disabled = True
            self.fields[
                'return_assembly_price'
            ].help_text = "Can't return assembly for this order"
        else:
            help_text = (
                f'How much of assembly do you want to return? '
                f'max for this order:<b> {max_possible_assembly_return}</b>'
            )
            self.fields['return_assembly_price'].help_text = help_text

    def get_max_possible_assembly_return(self, order, order_items):
        if not order.assembly:
            return Decimal('0.00')

        last_invoice = (
            Invoice.objects.filter(
                status__in=[
                    InvoiceStatus.ENABLED,
                    InvoiceStatus.CORRECTING,
                    InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
                ],
                order_id=order.id,
            )
            .order_by('id')
            .last()
        )
        assembly_invoice_item = None
        if last_invoice:
            assembly_invoice_item = (
                last_invoice.invoice_items.filter(item_type=InvoiceItemType.ASSEMBLY)
                .order_by('id')
                .last()
            )
        if assembly_invoice_item:
            assembly_price = assembly_invoice_item.gross_price
        else:
            assembly_price = sum(
                [item.get_assembly_price() * item.quantity for item in order_items]
            )
        if not assembly_price > 0:
            return Decimal('0.00')

        in_progress_return_assembly_price = self.get_in_progress_return_assembly_price(
            order.id
        )
        # subtract assembly price from started, but not finished free returns
        max_possible_return = assembly_price - in_progress_return_assembly_price
        return Decimal(max_possible_return).quantize(Decimal('.01'))

    @staticmethod
    def get_in_progress_return_assembly_price(order_id):
        free_returns = FreeReturn.objects.filter(
            orderitem__order_id=order_id,
            status__lte=FreeReturnStatusChoices.CORRECTION_REQUEST,
        )
        return free_returns.aggregate(
            return_assembly_price_sum=Coalesce(
                Sum('return_assembly_price'), Value(Decimal('0.0'))
            )
        )['return_assembly_price_sum']

    def clean(self):
        return_assembly_price = self.cleaned_data['return_assembly_price']
        order_items = self.cleaned_data['order_item']
        order = Order.objects.get(pk=self.pk)
        max_possible_assembly_return = self.get_max_possible_assembly_return(
            order, order_items
        )
        if return_assembly_price > max_possible_assembly_return:
            raise ValidationError(
                f'Exceeded max possible assembly return: '
                f'{return_assembly_price} but max: {max_possible_assembly_return}'
            )
        super().clean()


class RegionCalculatorForm(forms.Form):
    currency = forms.ModelChoiceField(queryset=Currency.objects.all())
    vat_type = forms.ChoiceField(choices=VatType.choices)
    price_brutto = forms.FloatField(required=False)
    regionalized_price_brutto = forms.FloatField(required=False)

    def clean(self):
        cleaned_data = super(RegionCalculatorForm, self).clean()
        if not cleaned_data.get('price_brutto') and not cleaned_data.get(
            'regionalized_price_brutto'
        ):
            raise forms.ValidationError(
                'Please fulfill Price brutto or Regionalized price',
            )


class ProformaForm(forms.ModelForm):
    class Meta:
        model = Invoice
        fields = (
            'issued_at',
            'sell_at',
            'additional_address_1',
            'additional_address_2',
            'additional_top',
            'currency_symbol',
            'force_outside_eu',
            'show_both_address',
        )

        widgets = {
            'additional_address_1': forms.Textarea(attrs={'rows': 2}),
            'additional_address_2': forms.Textarea(attrs={'rows': 2}),
            'additional_top': forms.Textarea(attrs={'rows': 2}),
        }


class OrderForm(forms.ModelForm):
    class Meta:
        model = Order
        fields = Order.ADDRESS_FIELDS
        widgets = {
            'country': forms.TextInput(attrs={'readonly': 'readonly'}),
            'vat': forms.TextInput(attrs={'readonly': 'readonly'}),
        }


class OrderInvoiceAddressForm(forms.ModelForm):
    class Meta:
        model = Order
        fields = Order.INVOICE_ADDRESS_FIELDS
        widgets = {
            'invoice_country': forms.TextInput(attrs={'readonly': 'readonly'}),
            'invoice_vat': forms.TextInput(attrs={'readonly': 'readonly'}),
        }


class InvoiceItemForm(forms.ModelForm):
    class Meta:
        model = InvoiceItem
        fields = (
            'id',
            'item_name',
            'item_material',
            'item_dimensions',
            'discount_value',
            'gross_price',
            'vat_rate',
            'net_weight',
            'gross_weight',
        )

    def save(self, commit=True):
        item = self.instance
        if not item.item_type:
            item.item_type = 0  # allow only items
        if not item.quantity:
            item.quantity = 1  # always one item
        vat_amount = (old_div(item.gross_price, (1 + item.vat_rate))) * item.vat_rate
        vat_amount = vat_amount.quantize(
            Decimal('.01'),
            rounding=ROUND_HALF_UP,
        )
        item.vat_amount = vat_amount
        item.net_value = item.gross_price - item.vat_amount
        item.net_price = item.net_value + item.discount_value
        return super().save(commit)


class UserSearchForm(forms.Form):
    user_id = forms.IntegerField(required=False)

    def __init__(self, *args, **kwargs):
        self.helper = FormHelper()
        self.helper.form_id = 'idm'
        self.helper.form_class = 'blueForms'
        self.helper.form_method = 'post'
        self.helper.form_action = 'submit_survey'

        self.helper.add_input(Submit('submit', 'Change'))
        super().__init__(*args, **kwargs)


class CacheableModelChoiceIterator(forms.models.ModelChoiceIterator):
    cache_key = 'users_choices'

    def __iter__(self):
        choices = cache.get(self.cache_key)
        if choices:
            for choice in choices:
                yield choice
        else:
            choices = list()
            super_iterator = super(
                CacheableModelChoiceIterator,
                self,
            ).__iter__()
            for choice in super_iterator:
                # New in Django 3.1:
                # First value from choice from super_iterator is of string type,
                # the rest is of type ModelChoiceIteratorValue. It happens when
                # the field can have the value None. So, for example, super_iterator has
                # a structure:
                # tuple(super_iterator) = (
                #   '', '---------',
                #   <ModelChoiceIteratorValue obj0>, 'user_0',
                #   <ModelChoiceIteratorValue obj1>, 'user_1',
                # )
                # ModelChoiceIteratorValue is not hashable so we need to get value
                # from it, but the string has no such attributes
                # and so we need this check.
                if isinstance(choice[0], ModelChoiceIteratorValue):
                    yield choice[0].value, choice[1]
                else:
                    yield choice
                choices.append(choice)
            cache.set(
                self.cache_key,
                choices,
                datetime.timedelta(days=1).total_seconds(),
            )


class UserChoiceField(forms.ModelChoiceField):
    iterator = CacheableModelChoiceIterator

    def label_from_instance(self, obj):
        full_name = ' '.join([obj.first_name, obj.last_name])
        return '{} {}'.format(full_name, obj.email).strip()


class CSNote(forms.Form):
    cs_note = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 4, 'cols': 55}), required=True
    )


class AddSampleBoxForm(forms.Form):
    order_id = forms.IntegerField()
    variant = forms.TypedChoiceField(choices=SampleBoxVariantEnum.choices(), coerce=int)


class ChangeLanguageForm(forms.Form):
    language = forms.ChoiceField(choices=LanguageEnum.choices)
    user_id = forms.IntegerField(widget=forms.HiddenInput())


class ChangeOrderStatusForm(forms.Form):
    status = forms.ChoiceField(
        choices=[
            choice for choice in OrderStatus.choices if choice[0] != OrderStatus.CART
        ]
    )


InvoiceItemsFormSet = forms.inlineformset_factory(
    Invoice,
    InvoiceItem,
    InvoiceItemForm,
    extra=0,
)


class UrlLabelModelChoiceField(forms.ModelChoiceField):
    def label_from_instance(self, obj):
        return format_html(
            mark_safe('<a href="{}" target="_blank">{}</a>'),
            reverse('cs_update_order', args=[obj.pk]),
            obj,
        )


class OrderSwitchItemOnHoldForm(forms.Form):
    source_order_item = forms.IntegerField()


class OrderSwitchItemReplacementForm(forms.Form):
    target_shelf_id = forms.IntegerField()
    quantity = forms.IntegerField(min_value=1)

    def __init__(self, *args, **kwargs):
        self.source_order_item = kwargs.pop('source_order_item')
        super().__init__(*args, **kwargs)
        self.fields['quantity'].initial = self.source_order_item.quantity

    def clean(self):
        cleaned_data = super().clean()
        target_shelf_id = cleaned_data['target_shelf_id']
        furniture_type = self.source_order_item.get_furniture_type()
        FurnitureModel = furniture_type_to_model(furniture_type)
        try:
            furniture_to_add = FurnitureModel.objects.get(pk=target_shelf_id)
        except FurnitureModel.DoesNotExist:
            raise ValidationError(
                f'{furniture_type.capitalize()} with this ID: '
                f"{target_shelf_id} doesn't exist",
                code='invalid_furniture_type',
            )
        else:
            cleaned_data['furniture_to_add'] = furniture_to_add
        return cleaned_data


class OrderSwitchRecalculationsForm(forms.Form):
    assembly = forms.BooleanField(required=False)
    without_assembly_price_change = forms.BooleanField(required=False)

    without_shelf_price_change = forms.BooleanField(required=False)

    def __init__(self, *args, **kwargs):
        self.order = kwargs.pop('order')
        super().__init__(*args, **kwargs)
        self.fields['assembly'].initial = self.order.assembly
        if not self.order.possible_shelf_assembly:
            self.fields['assembly'].disabled = True
            self.fields['without_assembly_price_change'].disabled = True
        else:
            help_text = (
                f'Assembly price change(if enabled): '
                f'{self.order.region_assembly_price_target_minus_source}'
            )
            self.fields['assembly'].help_text = help_text

        if self.order.assembly:
            self.fields['assembly'].disabled = True
        else:
            self.fields['without_assembly_price_change'].disabled = True

    def clean(self):
        cleaned_data = super().clean()
        assembly = cleaned_data['assembly']
        cleaned_data['should_edit_assembly'] = assembly and self.order.assembly
        cleaned_data['should_add_assembly'] = assembly and not self.order.assembly
        return cleaned_data


class OrderAddExtraDiscountVoucherReplacementForm(forms.Form):
    KIND_OF_VALUE = 'percentage'
    KIND_OF_CODE = 'code'

    KIND_OF_CHOICES = (
        (KIND_OF_VALUE, 'Percentage'),
        (KIND_OF_CODE, 'Code'),
    )

    kind_of = forms.ChoiceField(
        choices=KIND_OF_CHOICES,
        required=True,
    )
    code = forms.CharField(
        required=False,
        max_length=255,
    )
    percentage = forms.DecimalField(
        required=False,
        min_value=Decimal('0.0'),
        max_value=Decimal('100.0'),
        help_text="Between 0 - 100% as it's percentage",
    )

    def __init__(self, *args, **kwargs):
        self.source_voucher = kwargs.pop('source_voucher')
        super().__init__(*args, **kwargs)
        if self.source_voucher and self.source_voucher.is_absolute():
            self.fields['kind_of'].choices = [(self.KIND_OF_CODE, 'Code')]

    def clean(self):
        cleaned_data = super().clean()
        if cleaned_data['kind_of'] == self.KIND_OF_CODE:
            voucher_code = cleaned_data.get('code', '')
            if voucher_code == '':
                raise ValidationError('Please fill required field')

            try:
                target_voucher = Voucher.objects.get(code=voucher_code)
            except Voucher.DoesNotExist:
                raise ValidationError(
                    f"Voucher with this Code: {voucher_code} doesn't exist",
                    code='invalid_voucher_code',
                )

            if not target_voucher.is_active():
                raise ValidationError(
                    f'Voucher with this Code: {voucher_code} is not active',
                    code='inactive_voucher_code',
                )
            if self.is_different_type_by_code(target_voucher):
                source_kind_of = self.source_voucher.get_kind_of_display()
                raise ValidationError(
                    f"Can't apply different voucher types. "
                    f'Use {source_kind_of} Voucher',
                    code='wrong_voucher_kind_of',
                )
            if self.is_lte_absolute(target_voucher):
                raise ValidationError(
                    f'Expected absolute: {target_voucher.value} is less or '
                    f'equal to current voucher',
                    code='invalid_voucher_value',
                )

        else:
            percentage = cleaned_data.get('percentage')
            if not percentage:
                raise ValidationError('Please fill required field')

            if self.is_different_type_by_percentage():
                source_kind_of = self.source_voucher.get_kind_of_display()
                raise ValidationError(
                    f"Can't apply different voucher types. "
                    f'Use {source_kind_of} Voucher',
                    code='wrong_voucher_kind_of',
                )

            if self.is_lte_percentage(percentage):
                raise ValidationError(
                    f'Expected percentage: {percentage} is less or equal to '
                    f'current voucher',
                    code='invalid_voucher_value',
                )

            target_voucher = get_or_create_extra_discount_voucher(
                percentage, VoucherType.PERCENTAGE
            )

        cleaned_data['target_voucher'] = target_voucher
        return cleaned_data

    def is_different_type_by_percentage(self):
        return self.source_voucher and self.source_voucher.is_absolute()

    def is_different_type_by_code(self, target_voucher):
        return (
            self.source_voucher
            and self.source_voucher.kind_of != target_voucher.kind_of
        )

    def is_lte_absolute(self, target_voucher: Voucher) -> bool:
        return (
            self.source_voucher
            and self.source_voucher.is_absolute()
            and self.source_voucher.value >= target_voucher.value
        )

    def is_lte_percentage(self, percentage: Decimal) -> bool:
        return self.source_voucher and self.source_voucher.value >= percentage


class UserProfileIsBusinessTypeForm(forms.ModelForm):
    class Meta:
        model = UserProfile
        fields = ('is_business_type',)

    def save(self, commit=True):
        user_profile = super().save(commit)
        if user_profile.is_business_type:
            export_serializer_to_big_query(
                'sales',
                'customers_b2b_log',
                model=UserProfile,
                serializer=UserProfileB2BBigQuery,
                write=bigquery.WriteDisposition.WRITE_APPEND,
                queryset=[user_profile],
            )
        return user_profile


class PromocodeAddForm(forms.Form):
    CODE_CHARACTERS = (
        ('all', 'Digits and Letters'),
        ('digits', 'Digits only'),
        ('letters', 'Letters only'),
    )

    kind_of = forms.ChoiceField(choices=VoucherType.choices, required=True)
    code = forms.CharField(
        max_length=250,
        required=False,
        help_text=(
            'Blank - automatically generated.<br/>Filled with how_many=1 - exact code.'
            '<br/>Filled with how_many>1 - random but containing the given code.'
        ),
    )
    value = forms.DecimalField(
        required=True,
        help_text='Number of percents for percentage.',
    )
    barter_value = forms.DecimalField(
        required=False,
        help_text='Only for influencers barter agreements, always absolute.',
    )
    barter_currency = forms.ChoiceField(
        required=False,
        help_text='Only for influencers barter agreements.',
    )
    uses = forms.IntegerField(
        initial=1,
        max_value=32767,
        required=True,
        help_text='Number of times on promocode can be used.',
    )
    end_date = forms.DateTimeField(
        required=False, widget=SelectDateWidget, help_text='Active until.'
    )
    limit_lower = forms.DecimalField(
        initial=500,
        required=True,
        help_text='Lower limit on the transaction overall value.',
    )
    limit_upper = forms.DecimalField(
        initial=100000,
        required=True,
        help_text='Upper limit on the transaction overall value.',
    )
    how_many = forms.IntegerField(
        required=True, help_text='How many promocodes like that to create.'
    )
    origin = forms.ChoiceField(
        required=True, choices=VoucherOrigin.choices, initial=None
    )
    characters = forms.IntegerField(
        required=False, initial=8, help_text='The length of promocode.'
    )
    code_characters = forms.ChoiceField(
        required=False,
        choices=CODE_CHARACTERS,
        initial='all',
        help_text='The kind of randomly generated characters.',
    )
    notes = forms.CharField(
        required=False,
        help_text=(
            'Additional notes e.g. reason for promocode creation or campaign name.'
        ),
    )
    excluded_furniture = forms.MultipleChoiceField(
        required=False,
        choices=ShelfType.choices_active(),
        widget=CheckboxSelectMultiple,
    )
    excluded_colors_type_01 = forms.MultipleChoiceField(
        required=False,
        choices=Type01Color.choices_active(),
        widget=CheckboxSelectMultiple,
    )
    excluded_colors_veneer_type_01 = forms.MultipleChoiceField(
        required=False,
        choices=VeneerType01Color.choices_active(),
        widget=CheckboxSelectMultiple,
    )
    excluded_colors_type_02 = forms.MultipleChoiceField(
        required=False,
        choices=Type02Color.choices_active(),
        widget=CheckboxSelectMultiple,
    )
    excluded_colors_type_03 = forms.MultipleChoiceField(
        required=False,
        choices=Type03Color.choices_active(),
        widget=CheckboxSelectMultiple,
    )
    excluded_colors_type_13 = forms.MultipleChoiceField(
        required=False,
        choices=Type13Color.choices_active(),
        widget=CheckboxSelectMultiple,
    )
    exclude_sample_box = forms.BooleanField(required=False)
    ignore_discount_on_invoice = forms.BooleanField(
        required=False,
        help_text='This will create an invoice for full price of the order. '
        'There will be no discounts included in the invoice',
    )
    email = forms.EmailField(
        required=False,
        help_text='Fill this field to have the created codes sent by email.',
    )
    delivery_discount = forms.DecimalField(
        required=False,
        help_text='Nominal value for absolute. Number of percents for percentage.',
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['barter_currency'].choices = Currency.objects.values_list(
            'id', 'code'
        )
        self.fields['barter_currency'].initial = Currency.objects.get(code='EUR').id

    def clean(self):
        cleaned_data = super().clean()
        if cleaned_data.get('origin') != str(
            VoucherOrigin.INFLUENCERS_BARTER_DEAL
        ) and cleaned_data.get('barter_value'):
            self.add_error(
                'barter_value',
                'Barter value is only for influencers barter deals',
            )
        return cleaned_data


class DeactivationUserForm(forms.ModelForm):
    class Meta:
        model = UserProfile
        fields = ('id',)

    def save(self, commit=False):
        user_profile = self.instance
        user_profile.user.is_active = False
        user_profile.user.email = self.get_deactivated_email(user_profile.user.email)
        user_profile.user.save(update_fields=['is_active', 'email'])

        user_profile.email = self.get_deactivated_email(user_profile.email)
        user_profile.invoice_email = self.get_deactivated_email(
            user_profile.invoice_email
        )
        user_profile.save(update_fields=['email', 'invoice_email'])
        return user_profile

    @staticmethod
    def get_deactivated_email(base_email):
        return f'deactivated_{base_email}'


class NewsletterUnsubscribeForm(forms.Form):
    email = forms.EmailField()
    unsubscribe_from_newsletter = forms.BooleanField(required=False)
    unsubscribe_from_saved_item_flow = forms.BooleanField(required=False)

    def clean(self):
        cleaned_data = super().clean()
        newsletter = cleaned_data.get('unsubscribe_from_newsletter')
        save_4_later = cleaned_data.get('unsubscribe_from_saved_item_flow')

        if not newsletter and not save_4_later:
            raise forms.ValidationError(
                'At least one unsubscribe source should be selected.'
            )

        return cleaned_data


class KlarnaAdjustmentCreateForm(forms.ModelForm):
    currency_symbol = forms.CharField(required=False)
    current_price = forms.DecimalField(required=False)

    class Meta:
        model = KlarnaAdjustment
        fields = (
            'change_type',
            'amount',
            'conversation_link',
            'reason',
            'invoice',
            'current_price',
        )
        widgets = {
            'reason': forms.Textarea(attrs={'rows': 3}),
            'invoice': forms.HiddenInput(),
            'current_price': forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for visible in self.visible_fields():
            visible.field.widget.attrs['class'] = 'form-control'
        invoice_id = kwargs['initial'].get('invoice')
        currency_symbol = self.get_currency_symbol(invoice_id)
        self.fields['currency_symbol'].initial = currency_symbol
        self.fields['currency_symbol'].disabled = True
        current_price = self.get_current_price(invoice_id)
        self.fields['current_price'].initial = current_price
        self.fields['current_price'].disabled = True

    @staticmethod
    def get_currency_symbol(invoice_id):
        invoice = Invoice.objects.get(id=invoice_id)
        return invoice.order.region.currency.code

    @staticmethod
    def get_current_price(invoice_id):
        invoice = Invoice.objects.get(id=invoice_id)
        current_price = invoice.sum_gross_price_from_invoice_items()
        return current_price

    def clean_invoice(self):
        invoice = self.cleaned_data['invoice']
        if invoice.status != InvoiceStatus.PROFORMA:
            self.add_error(None, forms.ValidationError('Can only change for pro forma'))

        if invoice.has_pending_klarna_adjustment:
            self.add_error(
                None, forms.ValidationError('There is already pending adjustment!')
            )

        amount = self.cleaned_data['amount']
        change_type = self.cleaned_data['change_type']
        invoice_gross_price = invoice.sum_gross_price_from_invoice_items()
        if (
            change_type == KlarnaPriceChangeType.DISCOUNT
            and amount > invoice_gross_price
        ):
            self.add_error(
                None,
                forms.ValidationError(f'Max possible discount: {invoice_gross_price}'),
            )
        return invoice

    def save(self, commit=False):
        klarna_adjustment = super().save(commit)
        klarna_adjustment.source = KlarnaSource.CUSTOMER_SERVICE
        return super().save(commit=True)
