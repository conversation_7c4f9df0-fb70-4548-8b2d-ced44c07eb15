from datetime import (
    datetime,
    timedelta,
)

from django.conf import settings
from django.db.models import Q

from celery import shared_task
from celery.utils.log import get_task_logger

from custom.metrics import task_metrics
from customer_service.enums import (
    MentionMeB2BRewardStatus,
    RefundStatus,
)
from customer_service.models import (
    CSOrder,
    CSUnsuccessfulPayments,
    CSUserProfile,
    MentionMeB2BReward,
    RefundInfo,
)
from customer_service.services.mention_me import (
    MentionMeB2BRewardService,
    RefundInfoService,
)
from mailing.templates import NotifyAccountingOrderMissingInvoice
from orders.models import Order
from user_profile.models import UserProfile

task_logger = get_task_logger(__name__)


@shared_task
@task_metrics
def cs_pending_failure_report():
    CSUnsuccessfulPayments.find_orders()


@shared_task
@task_metrics
def cs_pending_failure_clean_report():
    CSUnsuccessfulPayments.clean_already_payed_orders()


@shared_task
def update_mention_me_b2_reward():
    rewards = MentionMeB2BReward.objects.filter(
        Q(status=MentionMeB2BRewardStatus.WAITING)
        | Q(
            status=MentionMeB2BRewardStatus.REFEREE_ORDER_NOT_FOUND,
            created_at__gt=datetime.now() - timedelta(days=14),
        )
    )
    service = MentionMeB2BRewardService()
    for reward in rewards:
        service.update(reward)


@shared_task
def update_refund_info_reward():
    rewards = RefundInfo.objects.filter(
        Q(status=RefundStatus.WAITING)
        | Q(
            status=RefundStatus.CORRECT_ORDER_NOT_FOUND,
            created_at__gt=datetime.now() - timedelta(days=14),
        )
    )
    service = RefundInfoService()
    for reward in rewards:
        service.update(reward)


@shared_task
def send_email_missing_invoice(order_id, free_return_id):
    for _, email in settings.ACCOUNTING_MISSING_INVOICE_RECIPIENTS:
        NotifyAccountingOrderMissingInvoice(
            to_address=email,
            data_html={
                'order_id': order_id,
                'free_return_id': free_return_id,
            },
        ).send()


@shared_task
def update_or_create_cs_user_profile(user_profile_id):
    user_profile = UserProfile.objects.get(pk=user_profile_id)
    CSUserProfile.objects.update_or_create_from_user_profile(user_profile)


@shared_task
def update_or_create_cs_order(order_id):
    order = Order.objects.get(pk=order_id)
    CSOrder.objects.update_or_create_from_order(order)
