import logging
import re
import uuid

from collections import defaultdict
from copy import deepcopy
from datetime import (
    date,
    datetime,
    timedelta,
)
from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    Optional,
)
from urllib.parse import (
    urljoin,
    urlparse,
)

from django.conf import settings
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import (
    models,
    transaction,
)
from django.db.models import (
    BooleanField,
    DecimalField,
    Q,
)
from django.db.models.aggregates import Sum
from django.db.models.expressions import (
    Case,
    When,
)
from django.db.models.functions import Coalesce
from django.http import HttpRequest
from django.template.defaultfilters import safe
from django.urls import reverse
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.html import (
    escape,
    format_html,
)
from django.utils.safestring import mark_safe
from django.utils.translation import gettext as _

import dateutil.parser
import pendulum

from rest_framework.request import Request
from safedelete.managers import SafeDeleteManager
from safedelete.models import SafeDeleteModel

from abtests.models import ABTest
from carts.services.cart_service import CartService
from checkout.enums import TypeformSurveyMode
from checkout.services.typeform_url_generator import TypeformUrlGenerator
from checkout.vat.validation import sanitize_vat
from custom.constants import VAT_EU
from custom.enums import (
    Furniture,
    ShelfType,
)
from custom.helpers import calculate_calendar_week_range
from custom.internal_api.dto import LogisticOrderDTO
from custom.internal_api.enums import Shipper
from custom.metrics import metrics_client
from custom.models import (
    Countries,
    Timestampable,
)
from custom.utils.delivery import TimeHelper
from custom.utils.slack import (
    notify_about_b2b_on_slack,
    notify_about_big_order_on_slack,
)
from custom.utils.url import get_request_source
from customer_service.enums import CSCorrectionRequestStatus
from events.choices import BrazeEventClasses
from events.domain_events.logistic_events import (
    ComplaintShippedEvent,
    OrderDeliveredEvent,
    OrderReadyToBeShippedEvent,
    OrderShippedEvent,
)
from events.domain_events.marketing_events import (
    MarketingExcludeUpdateEvent,
    ProductPassportReadyEvent,
)
from events.domain_events.transact_events import (
    OrderSummaryRequestEvent,
    PaymentEntryEvent,
    PurchaseAttributesUpdateEvent,
    PurchaseEvent,
    ResendPaymentConfirmationRequestEvent,
)
from events.models import Event
from events.utils import get_sellable_items_sorted_ids
from gallery.enums import FurnitureStatusEnum
from gallery.mixins import (
    CartOrderMixin,
    SellableItemMixin,
)
from gallery.models import SampleBox
from gallery.serializers import furniture_serializer_class_factory
from invoice.choices import (
    InvoiceSource,
    InvoiceStatus,
)
from invoice.models import (
    DOMESTIC_RELEASE_DATETIME,
    Invoice,
    InvoiceDomestic,
    ProformaLogisticFreeMaterialSampleOutsideEUInvoice,
)
from logger.models import (
    Log,
    LoggerMixin,
)
from mailing.models import Customer
from mailing.templates import (
    OrderPaymentAuthorisedMail,
    OrderPaymentAuthorisedMailSampleSet,
    OrderPlacedReportMail,
    get_referral_context_for_email,
)
from orders.choices import OrderSource
from orders.enums import (
    OrderStatus,
    OrderType,
    VatChoices,
)
from orders.managers import (
    AnaliticsCustomQueryset,
    CustomOrderManager,
    OrderManager,
    OrderToProductionInvalidManager,
)
from orders.mixins import (
    OrderAddExtraDiscountMixin,
    OrderSplitSampleAndFurnitureMixin,
    OrderSwitchStatusTransitionsMixin,
)
from orders.order_notes import PRODUCTION_MIX_NOTE
from orders.services.vat_details import VatDetailsGetter
from orders.utils import (
    add_order_context,
    check_if_customer_is_returning_and_create,
    has_free_assembly_service,
    is_business_order_or_email,
    log_unexpected_status_change,
    sanitise_postal_code,
)
from payments.constants import (
    KLARNA_B2B_PAYMENT_METHOD,
    KLARNA_PAYMENT_METHODS,
)
from pricing_v3.models import (
    ItemPriceAbstractModel,
    PriceAbstractModel,
)
from pricing_v3.models.mixins import AggregateItemPriceMixin
from pricing_v3.services.item_price_calculators import OrderItemPriceCalculator
from pricing_v3.services.price_calculators import OrderPriceCalculator
from producers.choices import (
    ProductPriority,
    ProductStatus,
    SourcePriority,
)
from regions.cached_region import get_region_data_from_request
from regions.constants import OTHER_REGION_NAME
from regions.mixins import RegionalizedMixin
from regions.models import (
    GeoCity,
    Region,
)
from user_profile.models import AddressDataAbstractModel
from vouchers.enums import VoucherOrigin
from vouchers.models import Voucher
from waiting_list.choices import WaitingListStatus

if TYPE_CHECKING:
    from carts.models import Cart
    from gallery.models import SellableItemAbstract

logger = logging.getLogger('orders')


class PaidOrderManager(models.Manager):
    def get_queryset(self):
        return AnaliticsCustomQueryset(self.model, using=self._db).filter(
            (
                Q(
                    status__in=[
                        OrderStatus.IN_PRODUCTION,
                        OrderStatus.SHIPPED,
                        OrderStatus.TO_BE_SHIPPED,
                        OrderStatus.DELIVERED,
                    ]
                )
                & Q(order_type=OrderType.CUSTOMER)
            )
            | (
                Q(
                    status__in=[
                        OrderStatus.IN_PRODUCTION,
                        OrderStatus.SHIPPED,
                        OrderStatus.TO_BE_SHIPPED,
                        OrderStatus.DELIVERED,
                    ]
                )
                & Q(order_type__in=[OrderType.CUSTOM_ORDER, OrderType.B2B])
            )
        )

    def yesterday_only(self):
        return self.get_queryset().yesterday_only()

    def lastweek_only(self):
        return self.get_queryset().lastweek_only()

    def last2week_only(self):
        return self.get_queryset().last2week_only()

    def previousweek_only(self):
        return self.get_queryset().previousweek_only()

    def lastmonth_only(self):
        return self.get_queryset().lastmonth_only()

    def previousmonth_only(self):
        return self.get_queryset().previousmonth_only()


class VoucheredOrderManager(PaidOrderManager):
    def get_queryset(self):
        return (
            super(VoucheredOrderManager, self)
            .get_queryset()
            .filter(used_promo__isnull=False)
        )


class OrderedByQuerySet(models.QuerySet):
    def company(self):
        return (
            self.annotate(
                has_vat=Case(
                    When(vat__exact='', then=False),
                    When(vat__isnull=False, then=True),
                    When(invoice_vat__exact='', then=False),
                    When(invoice_vat__isnull=False, then=True),
                    default=False,
                    output_field=BooleanField(),
                )
            )
            .annotate(
                has_company_name=Case(
                    When(company_name__exact='', then=False),
                    When(company_name__isnull=False, then=True),
                    When(invoice_company_name__exact='', then=False),
                    When(invoice_company_name__isnull=False, then=True),
                    default=False,
                    output_field=BooleanField(),
                )
            )
            .filter(has_vat=True, has_company_name=True)
        )


class OfflineReferralOrderManager(VoucheredOrderManager):
    def get_queryset(self):
        return Order.objects.filter(used_promo__origin=VoucherOrigin.REFERRAL)


class Order(
    LoggerMixin,
    CartOrderMixin,
    PriceAbstractModel,
    RegionalizedMixin,
    OrderSwitchStatusTransitionsMixin,
    OrderSplitSampleAndFurnitureMixin,
    OrderAddExtraDiscountMixin,
    AddressDataAbstractModel,
):
    raw_id_fields = 'voucher'

    BIG_ORDER_TOTAL_NET_PRICE = Decimal('7000')

    MOBILE_SRC = (
        OrderSource.MOBILE_ANDROID,
        OrderSource.MOBILE_IPHONE,
        OrderSource.WEB_MOBILE,
    )

    TABLET_SRC = (OrderSource.MOBILE_ANDROID_TABLET, OrderSource.MOBILE_IPAD)

    DESKTOP_SRC = (OrderSource.WEB_DESKTOP,)

    VIP_PROMO_CODE_SUFFIX = 'xtylko'
    DESIGN_CHANGE_PROMO_CODE_PREFIX = 'des'

    CHANGE_REGION_ITEMS_LIMIT = 10
    FAST_TRACK_BUSINESS_DAYS_OFFSET = 5

    parent_order = models.ForeignKey(
        'Order',
        help_text='parent order, for suborders and complaints',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='suborders',
    )
    cached_items_type = models.CharField(max_length=64, blank=True, null=True)

    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    status = models.IntegerField(
        choices=OrderStatus.choices,
        default=OrderStatus.DRAFT,
        db_index=True,
    )
    status_previous = models.IntegerField(
        choices=OrderStatus.choices,
        default=OrderStatus.DRAFT,
    )

    order_type = models.IntegerField(
        choices=OrderType.choices,
        default=OrderType.CUSTOMER,
    )
    order_notes = models.TextField(blank=True, default='')

    order_source = models.IntegerField(
        choices=OrderSource.choices,
        default=OrderSource.UNKNOWN_SOURCE,
    )

    sent_invoice_date = models.DateTimeField(null=True, blank=True)

    order_pretty_id = models.CharField(max_length=50, blank=True, null=True)

    package_tracking = models.CharField(max_length=150, blank=True, null=True)
    paymant_status_history = models.TextField(null=True, blank=True)

    returning_client = models.BooleanField(default=False)

    is_estimated_delivery_date = models.BooleanField(default=False)

    token = models.CharField(max_length=150, blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)
    estimated_delivery_time = models.DateTimeField(
        verbose_name='estimated production date',
        blank=True,
        null=True,
    )
    estimated_delivery_time_log = models.JSONField(default=list)

    first_item_added_at = models.DateTimeField(null=True, blank=True)
    items_changed_at = models.DateTimeField(null=True, blank=True)
    price_updated_at = models.DateTimeField(null=True, blank=True)
    placed_at = models.DateTimeField(null=True, blank=True)
    paid_at = models.DateTimeField(null=True, blank=True, db_index=True)
    settled_at = models.DateTimeField(null=True, blank=True)
    payable_booking_date = models.DateTimeField(blank=True, null=True)

    notes = models.TextField(blank=True, default='')

    hard_parking = models.BooleanField(default=False)
    above_3rd_floor = models.BooleanField(default=False)
    no_elevator = models.BooleanField(null=True, default=None)
    floor_number = models.IntegerField(null=True, blank=True)
    invoice_for_company = models.BooleanField(default=False)
    chosen_payment_method = models.CharField(max_length=32, null=True, blank=True)

    additional_text = models.CharField(max_length=128, null=True, blank=True)

    used_promo_config = models.ForeignKey(
        'promotions.PromotionConfig',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    currency = models.ForeignKey(
        'regions.Currency',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )

    order_placed_email_sent = models.BooleanField(default=False)
    showed_confirmation = models.BooleanField(default=False)
    klarna_email_notification_for_accounting = models.BooleanField(default=False)
    klarna_capture_attempt_date = models.DateTimeField(null=True, blank=True)

    serialized_logistic_info = models.JSONField(default=list, blank=True)

    is_chargeback = models.BooleanField(null=True)

    ab_tests = models.JSONField(null=True, blank=True)

    cs_notes = models.TextField(
        blank=True,
        default='',
        verbose_name=_('Customer service notes'),
    )
    delay_notification_sent = models.BooleanField(default=False)

    objects = OrderManager()
    paid_orders = PaidOrderManager()
    vouchered_orders = VoucheredOrderManager()
    ordered_by = OrderedByQuerySet.as_manager()

    SERIALIZATION_FIELDS = ['serialized_logistic_info']

    class Meta:
        verbose_name = 'Order detail'
        verbose_name_plural = 'Orders'
        ordering = [
            '-id',
        ]
        indexes = [models.Index(fields=['email'])]

    def get_cart(self) -> Optional['Cart']:
        return getattr(self, 'cart', None)

    @property
    def is_order(self) -> bool:
        return True

    def update_ab_tests(
        self,
        request: Request,
        save: bool = True,
    ) -> bool:
        ab_tests = self.ab_tests or {}
        active_tests = ABTest.objects.get_active_tests_cached_list(
            get_region_data_from_request(request)
        )
        updated = False

        for active_test in active_tests:
            if active_test is not None and active_test.codename not in ab_tests:
                if cookie_val := request.COOKIES.get(active_test.codename):
                    ab_tests[active_test.codename] = cookie_val == 'ok'
                    updated = True
                elif session_val := request.session.get(active_test.codename):
                    ab_tests[active_test.codename] = session_val
                    updated = True

        if updated:
            self.ab_tests = ab_tests

        if updated and save:
            self.save(update_fields=['ab_tests'])

        return updated

    def save_serialization_only(self, *args, **kwargs):
        """
        It's dedicated save that should be called with update_fields parameter
        with SERIALIZATION_FIELDS only, to avoid side effects from our custom save
        """
        update_fields = kwargs.get('update_fields')
        assert set(update_fields).issubset(self.SERIALIZATION_FIELDS)
        return super().save(*args, **kwargs)

    def save(self, *args, **kwargs):
        from customer_service.models import CSOrder
        from customer_service.tasks import update_or_create_cs_order

        super().save(*args, **kwargs)
        if CSOrder.should_update_or_create_from_order(self):
            transaction.on_commit(lambda: update_or_create_cs_order.delay(self.pk))

    def set_serialized_logistic_info(self, serialized_logistic_order):
        update = False

        for idx, logistic_order in enumerate(self.serialized_logistic_info):
            if logistic_order['id'] == serialized_logistic_order['id']:
                self.serialized_logistic_info[idx] = serialized_logistic_order
                update = True
                break

        if not update:
            self.serialized_logistic_info.append(serialized_logistic_order)

        self.save_serialization_only(update_fields=['serialized_logistic_info'])

    def set_serialized_logistic_info_list(self, serialized_logistic_orders):
        self.serialized_logistic_info = serialized_logistic_orders
        self.save_serialization_only(update_fields=['serialized_logistic_info'])

    def delete_serialized_logistic_info(self, logistic_order_id):
        self.serialized_logistic_info = [
            serialized_logistic_order
            for serialized_logistic_order in self.serialized_logistic_info
            if serialized_logistic_order['id'] != logistic_order_id
        ]

        self.save_serialization_only(update_fields=['serialized_logistic_info'])

    @property
    def logistic_info(self):
        return [
            LogisticOrderDTO(**logistic_order)
            for logistic_order in self.serialized_logistic_info
        ]

    def get_logistic_order_for_sample_box(self):
        return self.get_logistic_order_by_order_type(Furniture.sample_box.value)

    def get_logistic_order_by_order_type(self, order_type):
        logistic_order_by_order_type = [
            logistic_order
            for logistic_order in self.logistic_info
            if logistic_order.order_type == order_type
        ]
        return logistic_order_by_order_type[0] if logistic_order_by_order_type else None

    def get_logistic_order_by_id(self, logistic_order_id):
        logistic_order_by_id = [
            logistic_order
            for logistic_order in self.logistic_info
            if logistic_order.id == logistic_order_id
        ]
        return logistic_order_by_id[0] if logistic_order_by_id else None

    @property
    def client_or_company_name(self):
        if self.first_name or self.last_name:
            return self.full_name
        return self.company_name

    @property
    def full_street_address(self):
        return f'{self.street_address_1} {self.street_address_2}'

    @property
    def full_address(self) -> str:
        """Full address with postal code and city."""
        address_fields = (
            self.street_address_1,
            self.street_address_2,
            self.postal_code,
            self.city,
        )
        return ' '.join(field or '' for field in address_fields)

    @property
    def full_phone(self) -> str:
        return f'{self.phone_prefix}{self.phone}'

    def get_child_orders_without_complaints(self):
        return self.suborders.exclude(order_type=OrderType.COMPLAINT)

    def get_complaint_related(self):
        complaints = list(self.complaints.all())
        if not complaints:
            return None
        # it should be prefetched object, last can provoke extra query
        return complaints[-1]

    def is_complaint(self):
        return self.order_type == OrderType.COMPLAINT

    def is_to_be_shipped_complaint(self) -> bool:
        complaint = self.get_complaint_related()
        if complaint:
            return complaint.reproduction and self.is_complaint()
        return False

    def is_to_be_shipped_complaint_express_replacement(self):
        complaint = self.get_complaint_related()
        if complaint:
            return self.is_to_be_shipped_complaint() and complaint.express_replacement
        return False

    def get_complaint_notification_type_display(self):
        complaint = self.get_complaint_related()
        if complaint:
            return complaint.get_notification_type_display()

    def get_complaint_notification_type(self):
        complaint = self.get_complaint_related()
        if complaint:
            return complaint.notification_type

    def __str__(self):
        return format_html(
            'Order ({id}) : {status} for user: {owner_id}/{first_name} {last_name}',
            id=self.id,
            status=self.get_status_display(),
            owner_id=self.owner_id,
            first_name=self.first_name,
            last_name=self.last_name,
        )

    # NOTE: COMPUTE ITEM PRICES BASED ON THE ORDER -
    # PRICES IN ORDER_ITEM CAN BE INCORRECT DUE TO TAXES!

    def update_order_notes_production_mix(self):
        from orders.internal_api.events import OrderRefreshEvent

        manufacturers = set(
            self.items.filter(product__manufactor__isnull=False).values_list(
                'product__manufactor__name', flat=True
            ),
        )

        manufacturers = sorted(list(manufacturers))
        prod_mix_info = PRODUCTION_MIX_NOTE.format(', '.join(manufacturers))
        order_notes = self.order_notes

        if len(manufacturers) < 2:
            return

        if prod_mix_info not in order_notes:
            prod_mix_regex = PRODUCTION_MIX_NOTE.format('.*')
            if re.search(prod_mix_regex, order_notes):
                self.order_notes = re.sub(prod_mix_regex, prod_mix_info, order_notes)
            else:
                self.order_notes = (
                    f'{order_notes}\n{prod_mix_info}' if order_notes else prod_mix_info
                )
            self.save(update_fields=['order_notes'])
            OrderRefreshEvent(self)

    def get_delivery_date(self):
        logistic_order = next(reversed(self.logistic_info), None)
        if getattr(logistic_order, 'delivered_date', None):
            return logistic_order.delivered_date.isoformat()
        return None

    @property
    def full_name(self):
        return f'{self.first_name} {self.last_name}'

    @property
    def iso_alpha_2_country_code(self):
        return (
            getattr(
                Countries.get_country_by_name(self.country),
                'iso_alpha_2_country_code',
                '',
            )
            if self.country
            else ''
        )

    def match_log(self):
        log = Log.objects.filter(model=self._meta.label).filter(
            Q(model_id=self.pk) | Q(model_id=None, data__contains=[self.pk])
        )
        return log

    def get_manufacturer_name(self):
        return (
            self.product_set.filter(
                manufactor__isnull=False,
            )
            .exclude(status=ProductStatus.ABORTED)
            .values_list('manufactor__name', flat=True)
            .first()
        )

    @staticmethod
    def find_klarna_orders():
        from orders.internal_api.clients import LogisticOrderAPIClient

        logistic_order_api_client = LogisticOrderAPIClient()
        order_ids = logistic_order_api_client.filter_sent_and_delivered_orders()

        return Order.objects.filter(
            chosen_payment_method__in=KLARNA_PAYMENT_METHODS,
            klarna_email_notification_for_accounting=False,
            id__in=order_ids,
        )

    @property
    def originally_estimated_delivery_time(self):
        if self.estimated_delivery_time_log:
            try:
                return dateutil.parser.parse(self.estimated_delivery_time_log[0])
            except (dateutil.parser.ParserError, TypeError, OverflowError):
                logger.exception('Error when parsing DateTime')
                return None
        return self.estimated_delivery_time

    def is_production_delayed(self):
        DELAY_IN_WEEKS = 1.3
        if self.estimated_delivery_time_log and self.estimated_delivery_time:
            return self.production_delayed_weeks() >= DELAY_IN_WEEKS
        return False

    def production_delayed_weeks(self):
        estimated_delivery_date = self.estimated_delivery_time.date()
        production_delay_in_weeks = (
            estimated_delivery_date - self.originally_estimated_delivery_time.date()
        ).days / 7.0
        return Decimal(production_delay_in_weeks).quantize(Decimal('.1'))

    def production_time_weeks(self):
        if not self.paid_at:
            return None
        actual_production_time = (
            self.estimated_delivery_time.date() - self.paid_at.date()
        ).days / 7.0
        return Decimal(actual_production_time).quantize(Decimal('.1'))

    @property
    def items_type(self):
        if (
            (
                self.cached_items_type is None
                or (
                    self.cached_items_type is not None
                    and len(self.cached_items_type) == 0
                )
            )
            and self.items.count() > 0
            and (self.parent_order is not None or self.suborders.count() == 0)
        ):
            self.cached_items_type = self.items.first().get_furniture_type()
            self.save(update_fields=['cached_items_type'])
            return self.cached_items_type
        else:
            return self.cached_items_type

    def get_items_type(self):
        if self.items.count() > 0:
            return self.items.all().first().get_furniture_type()
        return None

    def get_free_return_status(self):
        """
        Function returns status of free return
        or None if no free return for order exists
        :return:
        """
        status = set()
        for item in self.items.all():
            if item.free_return is not None:
                status.add(
                    f'{item.free_return.note} - {item.free_return.get_status_display()}'
                )
        if len(status) == 0:
            return None
        return ', '.join(status)

    get_free_return_status.short_description = 'Free Return status'

    def get_free_return_data(self):
        """
        free return data flattened for customer service template
        :return:
        """
        free_returns = {}
        for item in self.items.all():
            if item.free_return is not None:
                if item.free_return.pk not in free_returns:
                    free_returns[item.free_return.pk] = {
                        'reason': set(),
                        'status': set(),
                        'note': set(),
                        'items': set(),
                    }
                free_returns[item.free_return.pk][
                    'is_packed'
                ] = item.free_return.is_packed
                free_returns[item.free_return.pk][
                    'is_need_packaging'
                ] = item.free_return.is_need_packaging
                free_returns[item.free_return.pk][
                    'is_send_asap'
                ] = item.free_return.is_send_asap
                free_returns[item.free_return.pk]['items'].add(item)
                free_returns[item.free_return.pk]['reason'].add(item.free_return.reason)
                free_returns[item.free_return.pk]['status'].add(
                    item.free_return.get_status_display()
                )
                free_returns[item.free_return.pk]['note'].add(item.free_return.note)
                free_returns[item.free_return.pk][
                    'return_assembly_price'
                ] = item.free_return.return_assembly_price
        return free_returns

    def get_chargeback_phase(self):
        logistic_order = self.logistic_info[0] if self.logistic_info else None
        if logistic_order is None:
            return 'przed batchowaniem, nie ma danych logistycznych'
        if logistic_order.delivered_date:
            return 'dostarczone do klienta'
        if logistic_order.sent_to_customer:
            return 'wysłane do klienta'
        if any(self.product_set.all().values_list('batch', flat=True)):
            return 'w produkcji, po batchowaniu'
        return 'przed batchowaniem'

    def is_france(self):
        return self.region.name == Countries.france.name

    def is_poland(self):
        return self.region.name == Countries.poland.name

    def is_united_kingdom(self):
        return self.country and self.country.lower() == Countries.united_kingdom.name

    def is_united_kingdom_lte_tax_threshold(self, region_total_diff: Decimal) -> bool:
        return (
            self.is_united_kingdom()
            and Decimal('0.0')
            < region_total_diff
            <= Invoice.UNITED_KINGDOM_TAX_THRESHOLD
        )

    def earliest_invoice_domestic_version_supported(self):
        first_invoice = (
            self.invoice_set.exclude(status=InvoiceStatus.PROFORMA)
            .order_by('id')
            .first()
        )

        first_invoice_after_exists = bool(
            first_invoice and first_invoice.issued_at >= DOMESTIC_RELEASE_DATETIME
        )
        return self.is_united_kingdom() and (
            first_invoice_after_exists or first_invoice is None
        )

    def earliest_invoice_domestic_version_not_supported(self):
        first_invoice = (
            self.invoice_set.exclude(status=InvoiceStatus.PROFORMA)
            .order_by('id')
            .first()
        )
        first_invoice_before_exists = bool(
            first_invoice and first_invoice.issued_at < DOMESTIC_RELEASE_DATETIME
        )

        return self.is_united_kingdom() and first_invoice_before_exists

    def is_united_kingdom_with_samples_only(self):
        return self.is_united_kingdom() and self.contains_only_samples

    def is_united_kingdom_with_furnitures(self):
        return self.is_united_kingdom() and not self.contains_only_samples

    def contains_only_one_content_type(self, content_type_name):
        if not self.items.all():
            return False
        return not self.items.exclude(content_type__model=content_type_name).exists()

    @property
    def contains_only_samples(self):
        return self.contains_only_one_content_type('samplebox')

    @property
    def contains_only_watties(self):
        return self.contains_only_one_content_type('watty')

    def get_customer_as_string(self, with_html=True):
        customer_name = f'{self.first_name} {self.last_name}'

        if self.company_name:
            new_line = '</br>' if with_html else '  '
            return format_html(f'{customer_name} {new_line}{self.company_name}')

        return format_html(customer_name)

    def get_country(self):
        if self.country is None or self.country.lower() in ['none', '', None]:
            return 'not set'
        if self.country.lower() in ['poland', 'polska', 'pl']:
            return Countries.poland.name
        if self.country.lower() in ['unitedkingdom', 'united_kingdom', 'uk']:
            return Countries.united_kingdom.name
        if self.country.lower() in ['au', 'austria']:
            return Countries.austria.name
        return self.country.lower()

    def admin_country(self):
        if self.country is None or self.country.lower() in ['none', '', None]:
            return self.owner.profile.registration_country
        else:
            return self.country

    admin_country.short_description = 'Country'

    def get_sell_channel(self):
        if self.order_source != OrderSource.UNKNOWN_SOURCE:
            if self.order_source in [
                OrderSource.MOBILE_IPHONE,
                OrderSource.MOBILE_ANDROID,
                OrderSource.MOBILE_ANDROID_TABLET,
                OrderSource.MOBILE_IPAD,
                OrderSource.MOBILE_NATIVE_IOS,
            ]:
                return 'app'
            elif self.order_source == OrderSource.REMOTE_API:
                return 'api'
            else:
                return 'web'
        return 'web'  # lets take all remaing orders as web

    @property
    def address_data(self):
        # TODO: Double check - looks like it's not used anywhere - can be deleted?
        """should be a delivery address"""
        return self.address_from_order_fields()

    def full_street(self):
        return '{} {}'.format(
            self.street_address_1,
            self.street_address_2 if self.street_address_2 else '',
        )

    def get_address_summary(self):
        aa = (
            escape(
                '%s %s %s, %s %s, %s %s, %s, %s, %s'
                % (
                    self.company_name,
                    self.first_name,
                    self.last_name,
                    self.street_address_1,
                    self.street_address_2,
                    self.postal_code,
                    self.city,
                    self.country,
                    self.phone,
                    self.email,
                )
            )
            + '<br>'
            + escape(f'{self.notes}')
            + '<br/>'
            + (
                '%s'
                % (
                    '<br/>'.join([escape(i) for i in self.order_notes.split('<br/>')])
                    if self.order_notes is not None
                    else ''
                )
            )
        )
        return mark_safe(aa)

    def get_address_data(self, force_outside_eu=False):
        """should be used for invoicing, returns address data
        from address fields prefixed with 'invoice' or not"""

        if self.different_billing_address:
            address_data = self.address_from_invoice_fields()
        else:
            address_data = self.address_from_order_fields()

        address_data['vat'] = self.vat
        if self.country is None and force_outside_eu is True:
            address_data['outside_eu'] = True
        else:
            address_data['outside_eu'] = (
                True
                if force_outside_eu is True
                or self.get_region().name == OTHER_REGION_NAME
                or (
                    self.country
                    and self.country.lower() in ['switzerland', 'israel', 'norway']
                )
                else False
            )
        return address_data

    def get_all_address_data(self):
        """returns address data from both prefixed and not address fields"""
        from collections import OrderedDict

        address_data = OrderedDict()
        all_address_fields = [*self.ADDRESS_FIELDS, *self.INVOICE_ADDRESS_FIELDS]

        for field_name in all_address_fields:
            address_data[field_name] = getattr(self, field_name)
        return address_data

    def get_invoice_id(self):
        if self.invoice_set.all().count() == 0:
            return '-'
        else:
            return self.invoice_set.all()[0].pretty_id

    def get_invoices(self):
        return self.invoice_set.filter(
            status__in=[
                InvoiceStatus.ENABLED,
                InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
                InvoiceStatus.CORRECTING,
            ]
        )

    def get_order_visual_status(self):
        if self.status in {OrderStatus.PAYMENT_PENDING}:
            return 'neutral'
        elif self.status in [
            OrderStatus.DRAFT,
            OrderStatus.CANCELLED,
            OrderStatus.PAYMENT_FAILED,
        ]:
            return 'error'
        else:
            return 'success'

    def is_payable(self):
        return self.status in [
            OrderStatus.DRAFT,
            OrderStatus.CANCELLED,
            OrderStatus.PAYMENT_FAILED,
        ]

    def is_klarna_payment(self):
        return self.chosen_payment_method in KLARNA_PAYMENT_METHODS

    def is_klarna_b2b_payment(self) -> bool:
        return self.chosen_payment_method == KLARNA_B2B_PAYMENT_METHOD

    def may_be_a_company(self):
        if self.company_name not in [None, ''] or self.invoice_company_name not in [
            None,
            '',
        ]:
            return True
        return False

    def is_company(self):
        if (self.vat not in [None, ''] or self.invoice_vat not in [None, '']) and (
            self.company_name not in [None, '']
            or self.invoice_company_name not in [None, '']
        ):
            return True
        return False

    @property
    def cleaned_phone_number(self):
        return (
            f'{self.phone_prefix}{self.phone.replace("phone", "")}'
            if self.phone
            else ''
        )

    def get_total_value_display(self):
        return self.display_regionalized(self.get_total_value())

    def change_region(self, new_region):
        logger.error(
            'WEIRDU SHITU! Order.change_region() '
            + 'is supposedly never called yet here we are. '
            + 'Changing from %s to %s when city is %s',
            self.region.name,
            new_region.name,
            self.city,
            extra={'order_id': self.id, 'owner': self.owner_id},
        )
        self.clear_methods_cache()

        country = new_region.countries.last() if new_region else None

        if country and country.region_vat:
            self.region_vat = True
        else:
            self.region_vat = False
        self.region = new_region

        has_t03_availability = self.region.name in settings.T03_REGION_KEYS

        for item in self.items.all():
            order_item = item.order_item
            is_t03 = getattr(order_item, 'is_t03_wardrobe', False)
            is_t03_sample = (
                isinstance(order_item, SampleBox)
                and order_item.box_variant.is_type_03_variant
            )
            if not has_t03_availability and (is_t03 or is_t03_sample):
                self._delete_order_item(item)
                continue

            item.change_region(new_region)

        if self.is_barter:
            self.barter_data.handle_region_change(new_region)

        OrderPriceCalculator(self).calculate(check_vat=True)
        new_country = new_region.get_country(without_cache=True)
        if new_country is not None:
            self.country = new_country.name

        self.save(update_fields=['country', 'region', 'region_vat'])

    def change_status(self, status_to_change, should_callback_logistic=True):
        from orders.internal_api.events import OrderStatusChangedEvent

        fields_to_update = ['status', 'status_previous']
        self._log_status_change(status_to_change)

        if status_to_change == OrderStatus.CANCELLED:
            self._handle_cancelled_status_change()
        elif status_to_change == OrderStatus.IN_PRODUCTION:
            self._handle_in_production_status_change()
        elif status_to_change == OrderStatus.TO_BE_SHIPPED:
            self._handle_to_be_shipped_status_change()
        elif status_to_change == OrderStatus.SHIPPED:
            self._handle_shipped_status_change()
        elif status_to_change == OrderStatus.DELIVERED:
            self._handle_delivered_status_change(current_status=self.status)
        elif status_to_change == OrderStatus.PAYMENT_PENDING:
            self.placed_at = timezone.now()
            fields_to_update.append('placed_at')
            self.change_products_to_ordered()
            if cart := self.get_cart():
                CartService(cart).change_status_to_payment_pending()
        elif status_to_change == OrderStatus.DRAFT:
            if cart := self.get_cart():
                CartService(cart).change_status_to_active()

        self.status_previous = self.status
        self.status = status_to_change
        self.save(update_fields=fields_to_update)
        if should_callback_logistic and status_to_change in [
            OrderStatus.CANCELLED,
            OrderStatus.IN_PRODUCTION,
            OrderStatus.TO_BE_SHIPPED,
            OrderStatus.SHIPPED,
            OrderStatus.DELIVERED,
        ]:
            OrderStatusChangedEvent(self)
        self._update_marketing_exclude_attributes()

        OrderStatusHistory.objects.create(
            order=self,
            status=self.status,
            previous_status=self.status_previous,
        )

    def _log_status_change(self, status_to_change: int) -> None:
        previous_statuses = [
            OrderStatus.IN_PRODUCTION,
            OrderStatus.SHIPPED,
            OrderStatus.DELIVERED,
            OrderStatus.TO_BE_SHIPPED,
        ]
        next_statuses = [
            OrderStatus.DRAFT,
            OrderStatus.CART,
            OrderStatus.PAYMENT_PENDING,
        ]
        if self.status in previous_statuses and status_to_change in next_statuses:
            log_unexpected_status_change(self, status_to_change)

    def _handle_cancelled_status_change(self) -> None:
        from orders.internal_api.events import LogisticOrderUndeliveredEvent

        if cart := self.get_cart():
            CartService(cart).change_status_to_draft()

        if len(self.logistic_info):
            logistic_order = self.logistic_info[-1]
            LogisticOrderUndeliveredEvent(logistic_order.id)

        proforma_invoice = self.invoice_set.filter(status=InvoiceStatus.PROFORMA).last()
        if self.is_klarna_payment() and proforma_invoice:
            Invoice.objects.create_proforma_neutralization(proforma_invoice)

    def _handle_in_production_status_change(self) -> None:
        from orders.tasks import upgrade_user_profile_to_business_type

        if is_business_order_or_email(self):
            upgrade_user_profile_to_business_type.delay(self.pk)

        self.set_returning_customer_if_matched()

    def _handle_shipped_status_change(self) -> None:
        if not (logistic_order := self.last_logistic_order):
            return

        if (
            logistic_order
            and (self.is_complaint() or hasattr(logistic_order, 'complaint_service'))
            and self.complaints.exists()
            and logistic_order.carrier
            and logistic_order.carrier.upper()
            in (Shipper.FEDEX.value, Shipper.TNT.value)
        ):
            ComplaintShippedEvent(
                user=self.owner,
                email=self.email,
                order_id=self.id,
                carrier=logistic_order.carrier,
                tracking_number=logistic_order.tracking_number,
                tracking_link=logistic_order.tracking_link,
            )
        else:
            if not self.order_pretty_id:
                self.create_pretty_id(suborders_count=self.suborders.count())

            OrderShippedEvent(
                user=self.owner,
                email=self.email,
                order_id=self.id,
                order_pretty_id=self.order_pretty_id,
                is_sample_order=self.contains_only_samples,
                carrier=logistic_order.carrier,
                tracking_number=logistic_order.tracking_number,
                tracking_link=logistic_order.tracking_link,
            )

    def _handle_delivered_status_change(self, current_status: int) -> None:
        if current_status != OrderStatus.DELIVERED:
            owners_language = self.owner.profile.language
            OrderDeliveredEvent(
                user=self.owner,
                email=self.email,
                order_id=self.id,
                typeform_url=TypeformUrlGenerator(
                    order=self,
                    language_code=owners_language.upper(),
                    mode=TypeformSurveyMode.POST_DELIVERY,
                ).get_url(),
                is_sample_order=self.contains_only_samples,
                has_assembly=self.assembly,
                postal_code=self.postal_code,
                furniture_category=self.items_type,
            )

        if self.email and not self.contains_only_samples:
            Customer.objects.update_or_create_customer_and_set_type(order=self)

    def _handle_to_be_shipped_status_change(self) -> None:
        if not (logistic_order := self.last_logistic_order):
            return

        if not self.order_pretty_id:
            self.create_pretty_id(suborders_count=self.suborders.count())

        packages = []
        for batch in logistic_order.get_packages:
            packages.extend(batch.get('packages', []))

        OrderReadyToBeShippedEvent(
            user=self.owner,
            email=self.email,
            order_id=self.id,
            order_pretty_id=self.order_pretty_id,
            packages=packages,
            total_weight=logistic_order.total_brutto_weight_string(),
            is_ups_shipper=(
                getattr(logistic_order.shipper, 'name', None) == Shipper.UPS.value
            ),
        )

    def set_returning_customer_if_matched(self):
        from orders.tasks import task_synchronize_data_for_returning_client

        order_owner_email = self.owner.email
        order_email = self.email.lower() if self.email else ''
        if order_email or order_owner_email:
            self.returning_client, customer = check_if_customer_is_returning_and_create(
                order=self,
            )
            if self.returning_client and customer:
                self.save(update_fields=['returning_client'])
                transaction.on_commit(
                    lambda: task_synchronize_data_for_returning_client.delay(
                        order_email, order_owner_email
                    )
                )

    def order_or_product_in_status_production_or_above(self):
        msg = ''
        if self.status in [
            OrderStatus.IN_PRODUCTION,
            OrderStatus.SHIPPED,
            OrderStatus.DELIVERED,
            OrderStatus.TO_BE_SHIPPED,
        ]:
            msg = 'Order {} in status {} '.format(self.pk, self.get_status_display())
        count = self.product_set.all().count()
        if count:
            msg += '{} product(s) already exists {}'.format(
                count, [x.id for x in self.product_set.all()]
            )
        if msg:
            return msg
        return False

    def get_total_price_for_big_order(self):
        queryset = self.items.exclude(
            product__priority__in=ProductPriority.on_hold_and_postponed_priorities()
        ).exclude(content_type__model='watty')

        return queryset.aggregate(total_price_net=Sum('price_net'))[
            'total_price_net'
        ] or Decimal('0.0')

    @property
    def is_big_order(self):
        total_price_net = self.get_total_price_for_big_order()
        return total_price_net > self.BIG_ORDER_TOTAL_NET_PRICE

    @property
    def is_b2b_order(self):
        return self.vat_type == VatChoices.VAT_EU.value

    @property
    def is_production_mix(self):
        manufacturers = set(
            self.items.filter(product__manufactor__isnull=False).values_list(
                'product__manufactor__name', flat=True
            )
        )

        return len(manufacturers) > 1

    @property
    def is_ready_for_logistic_split(self):
        return (
            not self.product_set.filter(manufactor=None).exists()
            and not self.product_set.filter(batch__locked_at__isnull=True).exists()
        )

    @property
    def move_to_production_service(self):
        from orders.services.process_to_production import MoveOrderToProduction

        return MoveOrderToProduction(self)

    def notify_about_big_order(self):
        producers_url = urljoin(
            settings.SITE_URL,
            reverse('admin:producers_product_changelist') + f'?q={self.id}',
        )
        cs_url = urljoin(
            settings.SITE_URL,
            reverse('cs_user_overview', kwargs={'pk': self.owner_id}),
        )
        area, products_info_text = self.get_products_types_info_text()
        total_price = self.get_total_price_for_big_order()
        text = (
            f'<!here> Big Order <{producers_url}|{self.id}> \n'
            f'{cs_url} \n'
            f'{products_info_text}, {area} m2, {total_price} eur net \n'
            f'Region {self.country} \n'
            f'{"AS" if self.assembly else "No AS"}'
        )
        notify_about_big_order_on_slack(text)

    def notify_about_b2b_order(self):
        order_link = f'https://tylko.com/admin/accounting/order_info/{self.id}'
        cs_url = urljoin(
            settings.SITE_URL,
            reverse('cs_user_overview', kwargs={'pk': self.owner_id}),
        )
        area, products_info_text = self.get_products_types_info_text()
        total_price = self.get_total_price_number()
        is_marked_by_cs_as_b2b = (
            ':white_check_mark:' if self.owner.profile.is_business_type else ':x:'
        )
        text = (
            f'B2B Order: {self.id}\n'
            f'Link {order_link}\n'
            f'CS: {cs_url} \n'
            f'{products_info_text}, {area} m2, {total_price} eur net \n'
            f'Region {self.country} \n'
            f'Is Business Client: {is_marked_by_cs_as_b2b}\n'
            f'Promo Code {self.promo_text} value: {self.promo_amount_net} net'
        )
        notify_about_b2b_on_slack(text)

    def get_products_types_info_text(self):
        from producers.models import Product

        products_by_type = defaultdict(int)
        area = 0
        products_query = self.product_set.exclude(
            priority__in=ProductPriority.on_hold_and_postponed_priorities()
        ).exclude(cached_product_type=Product.WATTY)
        for product in products_query:
            products_by_type[product.cached_shelf_type] += 1
            area += product.cached_area or 0
        products_by_type_text = ' + '.join(
            [
                f'{product_type} x {type_no}'
                for product_type, type_no in products_by_type.items()
            ]
        )
        products_info = (
            f'MIX {products_by_type_text}'
            if len(products_by_type) > 1
            else products_by_type_text
        )
        return round(area, 2), products_info

    @property
    def is_influ_order(self):
        if self.promo_text and self.used_promo:
            return (
                self.promo_text.lower().endswith(self.VIP_PROMO_CODE_SUFFIX)
                and self.used_promo.is_influencers_origin()
            )
        return False

    @property
    def is_vip_order(self):
        if self.promo_text and self.used_promo:
            return (
                self.promo_text.lower().endswith(self.VIP_PROMO_CODE_SUFFIX)
                and not self.used_promo.is_influencers_origin()
            )
        return False

    @property
    def is_design_change_needed(self):
        if self.promo_text:
            return self.promo_text.lower().startswith(
                self.DESIGN_CHANGE_PROMO_CODE_PREFIX
            )
        return False

    def set_order_notes(self, value, commit=True):
        self.order_notes += f'\n{value}' if self.order_notes else value
        if commit:
            self.save(update_fields=['order_notes'])

    def copy_address_from_profile(self):
        profile = self.owner.profile

        self.email = profile.email
        self.first_name = profile.first_name
        self.last_name = profile.last_name
        self.company_name = profile.company_name
        self.street_address_1 = profile.street_address_1
        self.street_address_2 = profile.street_address_2
        self.city = profile.city
        self.postal_code = profile.postal_code
        self.country = profile.country
        self.country_area = profile.country_area
        self.phone = profile.phone
        self.phone_prefix = profile.phone_prefix
        self.notes = profile.notes
        self.vat = profile.vat
        self.region = profile.region

        if profile.invoice_company_name:
            self.invoice_email = profile.invoice_email
            self.invoice_first_name = profile.invoice_first_name
            self.invoice_last_name = profile.invoice_last_name
            self.invoice_vat = profile.invoice_vat
            self.invoice_company_name = profile.invoice_company_name
            self.invoice_street_address_1 = profile.invoice_street_address_1
            self.invoice_street_address_2 = profile.invoice_street_address_2
            self.invoice_city = profile.invoice_city
            self.invoice_postal_code = profile.invoice_postal_code
            self.invoice_country = profile.invoice_country
            self.invoice_country_area = profile.invoice_country_area

        self.save()

    def copy_address_to_profile(self):
        profile = self.owner.profile

        profile.email = self.email
        profile.first_name = self.first_name
        profile.last_name = self.last_name
        profile.company_name = self.company_name
        profile.street_address_1 = self.street_address_1
        profile.street_address_2 = self.street_address_2
        profile.city = self.city
        profile.postal_code = self.postal_code
        profile.country = self.country
        profile.country_area = self.country_area
        profile.phone = self.phone
        profile.phone_prefix = self.phone_prefix
        profile.notes = self.notes
        profile.vat = self.vat

        if profile.region is not None and profile.country != self.country:
            profile.change_region_by_country(self.country)

        if self.invoice_company_name or self.company_name:
            profile.invoice_email = self.invoice_email
            profile.invoice_first_name = self.invoice_first_name
            profile.invoice_last_name = self.invoice_last_name
            profile.invoice_vat = self.invoice_vat
            profile.invoice_company_name = self.invoice_company_name
            profile.invoice_street_address_1 = self.invoice_street_address_1
            profile.invoice_street_address_2 = self.invoice_street_address_2
            profile.invoice_city = self.invoice_city
            profile.invoice_postal_code = self.invoice_postal_code
            profile.invoice_country = self.invoice_country
            profile.invoice_country_area = self.invoice_country_area

        profile.save()

    def change_products_to_ordered(self):
        for item in self.items.all():
            item.order_item.furniture_status = FurnitureStatusEnum.ORDERED
            item.order_item.save(update_fields=['furniture_status'])

    def change_products_as_draft(self):
        for item in self.items.all():
            item.order_item.furniture_status = FurnitureStatusEnum.DRAFT
            item.order_item.save(update_fields=['furniture_status'])

    def get_country_short(self):
        try:
            country_code = Countries.get_all()[str(self.country.lower())].code
        except Exception:
            country_code = '??'
        return country_code

    def create_pretty_id(self, suborders_count=1):
        self.order_pretty_id = self.id
        if not self.token:
            self.token = uuid.uuid4().hex
        self.save(update_fields=['order_pretty_id', 'token'])

    def get_status_text(self):
        return self.get_status_display()

    get_status_text.short_description = 'Status'

    def clear_promo(self, commit=True):
        self.promo_text = ''
        self.used_promo = None
        self.used_promo_config = None
        self.clear_promo_amounts()
        if commit:
            self.save(
                update_fields=[
                    'promo_text',
                    'used_promo_config',
                    'used_promo',
                    'promo_amount',
                    'promo_amount_net',
                    'region_promo_amount',
                    'region_promo_amount_net',
                ]
            )
            self.items.update(region_promo_value=Decimal('0'))

            if barter_data := OrderBarterData.objects.filter(order=self):
                barter_data.delete()

    def update_values_if_invoice_ignored_promo_used(self) -> None:
        if self.used_promo and self.used_promo.ignore_on_invoice:
            OrderPriceCalculator(self).calculate(
                recalculate_items=False,
                ignore_vouchers_check=True,
            )
            self.clear_promo_amounts(save=True)

    @property
    def is_barter(self):
        return hasattr(self, 'barter_data')

    def get_order_country(self):
        if self.region_vat:
            return self.region.get_country()

    # TODO: remove unused
    def _sanitize_order_vat(self, country: Countries = None) -> Optional[str]:
        country = country or self.get_country()
        vat_to_be_checked = self.vat or self.invoice_vat
        country_checked = (
            self.invoice_country if self.different_billing_address else self.country
        )
        if vat_to_be_checked and country != Countries.poland.name:
            return sanitize_vat(vat_to_be_checked, country_checked)

    def is_full_barter_deal(self) -> bool:
        if (
            self.is_barter
            and self.barter_data.barter_deal.voucher.is_legit_barter_deal(self.region)
        ):
            return self.barter_data.get_order_value_with_barter_extracted() <= 0
        return False

    def is_free_united_kingdom_samples_only(self):
        return (
            self.is_united_kingdom()
            and (self.is_free() or self.chosen_payment_method == 'full_promocode')
            and self.contains_only_samples
        )

    def get_total_price_number(self):
        if self.total_price is None:
            OrderPriceCalculator(self).calculate(check_vat=True)
        return self.get_total_value()

    def calculate_total_price_for_items(self, items: list['OrderItem']) -> float:
        """Additional method to calculate order items price for vouchers."""
        total = 0
        vat_details = VatDetailsGetter(self)
        completed_target_order_items = self.completed_target_order_items.all()
        incomplete_items = set(items) - set(completed_target_order_items)
        for item in incomplete_items:
            OrderItemPriceCalculator(item).calculate(
                vat_rate=vat_details.vat_rate,
                # if order is B2B, we don't want to save recalculations for item
                with_save=not vat_details.vat_type.is_vat_exempt,
            )
            total += item.aggregate_region_price + item.aggregate_region_delivery_price
        return total

    def get_shelf_price_as_number(self):
        result = self.items.aggregate(
            price__sum=Coalesce(
                Sum('region_price'),
                0,
                output_field=DecimalField(),
            )
        )['price__sum']
        if result == 0:
            result = self.items.aggregate(
                price__sum=Coalesce(
                    Sum('price'),
                    0,
                    output_field=DecimalField(),
                )
            )['price__sum']
        return result

    def get_shelf_price(self):
        return self.display_regionalized(self.get_shelf_price_as_number())

    def get_assembly_price_display(self, assembly_price_as_number=None):
        if assembly_price_as_number is None:
            return self.display_regionalized(self.get_assembly_price())
        else:
            return self.display_regionalized(assembly_price_as_number)

    def get_assembly_status(self):
        logistic_order = next(reversed(self.logistic_info), None)
        if getattr(logistic_order, 'assembly_service', None):
            return logistic_order.assembly_service.get_status_display()

    def get_vat_amount_number(self):
        # return sum([item.vat_amount for item in self.items.all()])
        return self.get_vat_value()

    def get_vat_amount(self):
        return self.display_regionalized(self.get_vat_amount_number())

    def get_promo_amount(self):
        return self.display_regionalized(self.region_promo_amount)

    def send_confirmation_email(self):
        if not self.order_pretty_id:
            self.create_pretty_id()

        self.order_placed_email_sent = True
        self.save(update_fields=('order_placed_email_sent',))
        self.send_order_placed_email()
        self.send_order_report_emails()

    def send_order_report_emails(self) -> None:
        # TODO: move to Braze: https://cstm-tasks.atlassian.net/browse/ECO-1825
        for name, email in settings.ORDER_REPORT_RECIPIENTS:
            OrderPlacedReportMail(email, {'order': self}).send()

    def unify_email_data(self, email_address):
        objects_to_change = (self, self.owner, self.owner.profile)
        for object in objects_to_change:
            setattr(object, 'email', email_address)
            object.save()

    def send_order_notifications(
        self,
        email_address: str,
        order_confirmation: bool,
        payment_confirmation: bool,
        invoice_email: bool,
    ) -> Optional[str]:
        self.unify_email_data(email_address)
        if order_confirmation:
            self.send_order_placed_email(force_send=True)
        if payment_confirmation:
            self.send_payment_confirmation()
        if invoice_email:
            try:
                self.send_invoice_email()
            except (ValueError, FileNotFoundError):
                return (
                    'File with invoice does not exists. Notification has not been sent'
                )

    def send_order_placed_email(self, force_send=False) -> None:
        """Send email with order summary.

        Emit event to Braze or send email directly from backend depending on the
        settings.PROCESS_TRANSACT_FLOWS_ON_BE variable.
        """

        if settings.PROCESS_TRANSACT_FLOWS_ON_BE:
            from orders.tasks import send_order_placed_email_directly

            send_order_placed_email_directly.delay(self.id)
        else:
            OrderSummaryRequestEvent(
                user=self.owner,
                user_id=self.owner.id,
                order_id=self.id,
                email=self.email,
                is_sample_order=self.contains_only_samples,
                items_ids=get_sellable_items_sorted_ids(self),
                force_emit=force_send,
            )

    def send_payment_confirmation(self):
        if not settings.PROCESS_TRANSACT_FLOWS_ON_BE:
            ResendPaymentConfirmationRequestEvent(
                user=self.owner,
                email=self.email,
                order_id=self.id,
                is_sample_order=self.contains_only_samples,
            )
        else:
            self._send_payment_confirmation()

    def _send_payment_confirmation(self):
        """Send payment confirmation email from backend.

        TODO: Remove after Braze flow is confirmed to stay.
        """
        from user_profile.models import RetargetingBlacklistToken

        if self.contains_only_samples:
            mail_template = OrderPaymentAuthorisedMailSampleSet
        else:
            mail_template = OrderPaymentAuthorisedMail

        mail_context = {
            'order': self,
            'blacklist_token': RetargetingBlacklistToken.get_or_create_for_email(
                email=self.email
            ).token,
            **get_referral_context_for_email(self.owner.profile.region),
        }
        mail = mail_template(
            self.email,
            mail_context,
            topic_variables={
                'order_pretty_id': self.order_pretty_id,
            },
        )
        mail.send(language=self.owner.profile.language)

    def send_invoice_email(self):
        if invoice := self.invoice_set.first():
            invoice.send_invoice_to_user()

    def add_item(self, item, quantity=1):
        gallery = item
        gallery.pk = None
        gallery.save()
        order_item = OrderItem(
            order=self,
            order_item=gallery,
            quantity=quantity,
            region=self.region,
            price=0,
            price_net=0,
            region_price=0,
            region_price_net=0,
            with_assembly=gallery.is_assembly_service_required,
        )

        order_item.free_assembly_service = False
        if has_free_assembly_service(order_item):
            order_item.free_assembly_service = True
            order_item.assembly_price = 0
            order_item.region_assembly_price = 0

        order_item.save()

        # todo: verify why no vat_rate in arg, it will get default 23%
        OrderItemPriceCalculator(order_item).calculate()
        self.items_changed_at = timezone.now()
        self.save(update_fields=['items_changed_at'])
        return order_item

    def remove_item(self, item):
        if item.furniture_status == FurnitureStatusEnum.DRAFT:
            item.deleted = True
            item.save(update_fields=['deleted'])
        self.items_changed_at = timezone.now()
        self.save(update_fields=['items_changed_at'])

    def unremove_item(self, item):
        if item and item.furniture_status == FurnitureStatusEnum.DRAFT:
            item.deleted = False
            item.save(update_fields=['deleted'])
        self.items_changed_at = timezone.now()
        self.save(update_fields=['items_changed_at'])

    def remove_all_items(self):
        for orderItem in self.items.all():
            item = orderItem.order_item
            if item.furniture_status == 0:
                item.deleted = True
                item.save()
        self.items.all().delete()
        self.items_changed_at = timezone.now()
        self.save(update_fields=['items_changed_at'])

    def create_suborder(self, order_items):
        suborder = deepcopy(self)
        suborder.pk = None
        suborder.promo_text = None
        suborder.used_promo = None
        suborder.used_promo_config = None
        suborder.promo_amount, suborder.region_promo_amount = 0, 0
        suborder.parent_order = self
        suborder.order_pretty_id = self.id
        suborder_delivery_days = order_items[0].order_item.get_delivery_time_days()
        suborder.estimated_delivery_time = timezone.now() + timedelta(
            days=suborder_delivery_days
        )
        suborder.cached_items_type = order_items[0].order_item.get_furniture_type()
        suborder.save()
        for order_item in order_items:
            order_item.pk = None
            order_item.order = suborder
            order_item.save()
        OrderPriceCalculator(suborder).calculate(check_vat=True)
        return suborder

    def get_longest_delivery_time_in_weeks_as_string(self):
        delivery_in_weeks = self.get_longest_delivery_time_in_weeks()
        # fallback for missing items
        if delivery_in_weeks == 0:
            return [6, 9]
        else:
            return [delivery_in_weeks, delivery_in_weeks + 2]

    def get_longest_delivery_time_in_weeks(self):
        items = self.items.all()
        if len(items) == 0:
            return 0
        else:
            delivery_time_weeks = []
            for x in items:
                order_item = x.order_item
                if order_item is not None:
                    delivery_time_weeks.append(order_item.get_delivery_time_max_week())
            if len(delivery_time_weeks) == 0:
                return 0
            else:
                return max(delivery_time_weeks)

    @classmethod
    def create_new_cart(
        cls,
        user: User,
        request: Optional[HttpRequest] = None,
    ) -> 'Order':
        return cls.create_new_order_instance(user, OrderStatus.CART, request)

    @classmethod
    def create_new_draft(cls, user: User) -> 'Order':
        return cls.create_new_order_instance(user, OrderStatus.DRAFT)

    @classmethod
    def create_new_order_instance(
        cls,
        user: User,
        status: OrderStatus,
        request: Optional[HttpRequest] = None,
    ) -> 'Order':
        """
        Class method to create a new cart (an instance of Order with status CART)

        This method creates a new cart (Order) object for the given user,
        sets the region if provided, determines the source of the order from
        the request, copies the address from the user's profile,
        saves the cart to the database, and then returns it.

        Parameters:
        user (User): The User object who is the owner of the cart.
        request (Request): The Request object, used to determine the source
            of the order.

        Returns:
        Order: The newly created and saved Order object representing a cart.
        """
        if status not in [OrderStatus.CART, OrderStatus.DRAFT]:
            raise NotImplementedError(f'Status {status} not allowed')
        instance = cls(
            owner=user,
            status=status,
            region=user.profile.region or Region.get_other(),
        )
        if request is not None:
            instance.order_source = get_request_source(request)
        instance.copy_address_from_profile()
        instance.save()
        return instance

    def update_after_promocode_for_whole_amount(self, request):
        order = self
        if order.status in [
            OrderStatus.PAYMENT_PENDING,
            OrderStatus.DRAFT,
            OrderStatus.CART,
        ]:
            # second place where status and paid_at can differ
            order.update_to_paid(is_klarna_payment=self.is_klarna_payment())

            order.update_values_if_invoice_ignored_promo_used()

            self.update_waiting_list_entry()

    def update_waiting_list_entry(self):
        waiting_list_entry = (
            self.waiting_list_entries.exclude(
                status=WaitingListStatus.TOKEN_USED_FOR_PURCHASE,
            )
            .order_by('-created_at')
            .first()
        )

        if waiting_list_entry:
            waiting_list_entry.update_status(
                status=WaitingListStatus.TOKEN_USED_FOR_PURCHASE,
                creator=waiting_list_entry.owner,
            )

    def change_estimated_delivery_time(self, estimated_delivery_time):
        from orders.tasks import add_estimated_delivery_time_for_big_query_export

        self.estimated_delivery_time = estimated_delivery_time
        self.estimated_delivery_time_log.append(
            Order.objects.get(pk=self.pk).estimated_delivery_time.isoformat()
        )
        self.save(
            update_fields=['estimated_delivery_time', 'estimated_delivery_time_log']
        )
        add_estimated_delivery_time_for_big_query_export(self)

    def get_estimated_delivery_time(self):
        # Assume that if this method is called,
        # client always wants to recalculate the edt.
        delivery_time = None

        # suborder.estimated_delivery_time is filled during create_suborders
        if len(self.suborders.all()) > 0:
            for suborder in self.suborders.all():
                suborder_delivery_time = suborder.estimated_delivery_time
                if delivery_time is None or suborder_delivery_time > delivery_time:
                    delivery_time = suborder_delivery_time

        # if homogenic items or something is wrong
        if self.items.exists():
            max_delivery_time = max(
                item.order_item.get_delivery_time_days() for item in self.items.all()
            )
            items_delivery_time = timezone.now() + timedelta(days=max_delivery_time)
            if not delivery_time or items_delivery_time > delivery_time:
                return items_delivery_time
        return delivery_time

    def get_estimated_delivery_time_left(self):
        if self.estimated_delivery_time:
            return TimeHelper((self.estimated_delivery_time - timezone.now()).days)
        else:
            estimated_delivery = self.get_estimated_delivery_time()
            if estimated_delivery is not None:
                return TimeHelper((estimated_delivery - timezone.now()).days)
            else:
                logger.error(
                    'Missing estimated delivery time left '
                    'in order %s, returning 28 days',
                    self.id,
                )
                return TimeHelper(28)

    def get_delivery_time(self) -> datetime:
        delivery_time = (
            self.estimated_delivery_time or self.get_estimated_delivery_time()
        )
        if not delivery_time:
            logger.debug(
                'Cannot get delivery range date for Order(id=%s), '
                'setting to default 28 days',
                self.id,
            )
            delivery_time = datetime.now() + timedelta(28)

        return delivery_time

    def get_delivery_time_with_offset(self, offset=None) -> datetime:
        if offset is None:  # can be 0
            offset = self.get_delivery_offset()
        delivery_time = self.get_delivery_time()
        return delivery_time + timedelta(days=offset)

    def get_date_range(self, offset=None):
        if offset is None:  # can be 0
            offset = self.get_delivery_offset()
        delivery_time = self.get_delivery_time()
        return calculate_calendar_week_range(delivery_time, offset)

    def date_range_formatted(self, offset=None):
        delivery_range_start, delivery_range_end = self.get_date_range(offset)

        if delivery_range_start.year != delivery_range_end.year:
            return (
                f'{delivery_range_start.strftime("%d.%m.%Y")}'
                f' - {delivery_range_end.strftime("%d.%m.%Y")}'
            )
        elif delivery_range_start.month != delivery_range_end.month:
            return (
                f'{delivery_range_start.strftime("%d.%m")}'
                f' - {delivery_range_end.strftime("%d.%m.%Y")}'
            )
        return (
            f'{delivery_range_start.strftime("%d")}'
            f' - {delivery_range_end.strftime("%d.%m.%Y")}'
        )

    @property
    def production_range_formatted(self):
        return self.date_range_formatted(offset=0)

    @property
    def delivery_range_formatted(self):
        return self.date_range_formatted()

    @property
    def days_since_expected_delivery(self):
        _, end_date_time = self.get_date_range()
        today = datetime.today()
        days_since_expected_delivery = (today - end_date_time).days
        if days_since_expected_delivery < 0:
            return 0
        return days_since_expected_delivery

    def get_delivery_offset(self):
        has_furniture = self.items.filter(
            content_type__model__in={Furniture.jetty.value, Furniture.watty.value}
        ).exists()

        if not has_furniture:
            return 7

        has_wardrobe = self.items.filter(
            content_type__model=Furniture.watty.value
        ).exists()

        return 21 if has_wardrobe else 14

    @property
    def lead_time(self):
        if self.paid_at:
            return (self.paid_at - self.owner.date_joined).total_seconds()
        return 0

    @property
    def last_logistic_order(self):
        return self.last_delivered_logistic_order

    @property
    def last_delivered_logistic_order(self):
        sorted_logistic_info = sorted(
            self.logistic_info,
            key=lambda lo: lo.delivered_date or date.min,
            reverse=True,
        )
        return sorted_logistic_info[0] if sorted_logistic_info else None

    def create_invoice(self):
        currency = self.get_region().currency
        invoice = Invoice.objects.create(
            order=self,
            sell_at=self.paid_at,
            corrected_sell_at=self.paid_at,
            issued_at=self.paid_at,
            currency_symbol=currency.symbol,
        )
        if self.is_united_kingdom_lte_tax_threshold(self.region_total_price):
            invoice.set_additional_total_when_united_kingdom_lte_tax_threshold(
                self.region_total_price
            )
        if self.is_united_kingdom():
            InvoiceDomestic.objects.create_domestic_from_normal_or_proforma(invoice)

        return invoice

    def create_proforma(self):
        now = timezone.now()
        invoice = Invoice.objects.create(
            order=self,
            sell_at=now,
            issued_at=now,
            status=InvoiceStatus.PROFORMA,
            currency_symbol=self.get_region().currency.symbol,
        )
        if self.is_united_kingdom_lte_tax_threshold(self.region_total_price):
            invoice.set_additional_total_when_united_kingdom_lte_tax_threshold(
                self.region_total_price
            )
        if self.is_united_kingdom():
            InvoiceDomestic.objects.create_domestic_from_normal_or_proforma(invoice)

        return invoice

    def create_proforma_invoice(self, ignore_discount=False, source=InvoiceSource.CSTM):
        now = timezone.now()
        invoice = Invoice(
            order=self,
            sell_at=now,
            issued_at=now,
            status=InvoiceStatus.PROFORMA,
            currency_symbol=self.get_region(dont_use_cache=True).currency.symbol,
            source=source,
        )
        invoice.save(ignore_discount=ignore_discount)
        if self.is_united_kingdom_lte_tax_threshold(self.region_total_price):
            invoice.set_additional_total_when_united_kingdom_lte_tax_threshold(
                self.region_total_price
            )
        invoice.create_pdf()
        if (
            self.earliest_invoice_domestic_version_supported()
            and source != InvoiceSource.LOGISTIC
        ):
            invoice_domestic = (
                InvoiceDomestic.objects.create_domestic_from_normal_or_proforma(invoice)
            )
            invoice_domestic.create_pdf()
        return invoice

    def create_logistic_free_material_sample_outside_eu_proforma(self) -> Invoice:
        now = timezone.now()
        invoice = ProformaLogisticFreeMaterialSampleOutsideEUInvoice(
            order=self,
            sell_at=now,
            issued_at=now,
            status=InvoiceStatus.PROFORMA,
            currency_symbol=self.get_region(dont_use_cache=True).currency.symbol,
            source=InvoiceSource.LOGISTIC_FREE_MATERIAL_SAMPLE_OUTSIDE_EU,
        )
        invoice.save(ignore_discount=True)
        invoice.create_pdf()
        return invoice

    def get_absolute_url(self):
        return f'/admin/orders/order/{self.id}/'

    def get_paid_order_with_same_email_count(self):
        return PaidOrders.objects.filter(email=self.email).count()

    def get_days_from_create(self):
        return (
            pendulum.today()
            .on(
                year=self.created_at.year,
                month=self.created_at.month,
                day=self.created_at.day,
            )
            .diff(pendulum.now())
            .in_days()
        )

    @staticmethod
    def deserialize_order_number(order_number):
        formats = (
            (r'^[A-Z]*\/([0-9]*\/){3}([0-9]*)\/[A-Z]*$', 2),
            (r'^[A-Z]*\/[0-9]*\/[0-9]{2}([0-9]*)\/[A-Z]*$', 1),
            (r'^[0-9]*$', 0),
        )
        for f, group_nr in formats:
            match = re.match(f, order_number)
            if match:
                return int(match.group(group_nr))
        return None

    @property
    def sanitised_postal_code(self):
        if not self.postal_code:
            return None
        return sanitise_postal_code(self.postal_code)

    @property
    def sanitised_invoice_postal_code(self):
        if not self.invoice_postal_code:
            return None
        return sanitise_postal_code(self.invoice_postal_code)

    @property
    def transaction_description(self):
        status_to_check = 'AUTHORISATION'
        transactions_summary = []
        for authorisation_transaction in self.transactions.filter(
            status=status_to_check
        ):
            #  first, lets find succeded ones
            notification = authorisation_transaction.notification_set.filter(
                code=status_to_check,
                success=True,
            ).first()
            if notification:
                transactions_summary.append(
                    {
                        'date': notification.event_date,
                        'success': True,
                        'payment_method': notification.payment_method,
                    }
                )
            else:
                notifications = authorisation_transaction.notification_set.filter(
                    code=status_to_check
                )
                if notifications:
                    for notification in notifications:
                        transactions_summary.append(
                            {
                                'date': notification.event_date,
                                'success': False,
                                'payment_method': notification.payment_method,
                                'refusal_reason': '{}, {}'.format(
                                    notification.reason,
                                    authorisation_transaction.return_data,
                                ),
                            }
                        )
                else:
                    transactions_summary.append(
                        {
                            'date': '-',
                            'success': False,
                            'payment_method': authorisation_transaction.payment_method,
                            'refusal_reason': 'NO NOTIFICATION',
                        }
                    )
        return transactions_summary

    def clear_prices_and_promo(self):
        self.clear_promo(commit=False)

        self.total_price = 0
        self.total_price_net = 0
        self.region_total_price = 0
        self.region_total_price_net = 0

        self.estimated_delivery_time = None
        self.placed_at = None
        self.paid_at = None

    @property
    def adyen_reference(self):
        if not settings.IS_PRODUCTION:
            domain = urlparse(settings.SITE_URL).netloc
            return f'order-{self.id}/{domain}'
        return 'order-{}'.format(self.id)

    def payment_provider_price(self) -> int:
        """
        Adyen requires prices in minor monetary unit, primer just major * 100.
        For our current currencies it's the same, but does not have to.
        """
        if self.is_barter:
            return int(self.barter_data.get_order_value_with_barter_extracted() * 100)
        return int(self.get_total_price_number() * 100)

    @property
    def adyen_price(self) -> int:
        """
        Adyen needs prices in minor monetary unit.
        If we introduce more currencies check if they minor unit is as well a 100
        times smaller than the major.
        Adyen's docs: https://docs.adyen.com/development-resources/currency-codes
        """
        return self.payment_provider_price()

    @property
    def primer_price(self) -> int:
        return self.payment_provider_price()

    def get_logistic_orders_brutto_weight(self):
        return sum(
            logistic_order.total_brutto_weight()
            for logistic_order in self.logistic_info
        )

    def get_complaints_brutto_weight(self):
        complaint_orders = self.suborders.filter(order_type=OrderType.COMPLAINT)
        if not complaint_orders.exists():
            return None

        complaints_brutto_weight = sum(
            order.get_logistic_orders_brutto_weight() for order in complaint_orders
        )
        return Decimal(complaints_brutto_weight).quantize(Decimal('.01'))

    def should_be_updated_to_paid(self, products_count=None):
        if products_count is None:
            products_count = self.product_set.all().count()
        return products_count == 0 and self.status in [
            OrderStatus.PAYMENT_PENDING,
            OrderStatus.DRAFT,
            OrderStatus.PAYMENT_FAILED,
        ]

    def success_payment_received(self, is_klarna_payment=False):
        _, created = OrderToProduction.objects.get_or_create(
            original_order=self,
            defaults={'is_klarna_payment': is_klarna_payment},
        )
        if not created:
            logger.exception(f'Payment for order {self.id} already received')

    def update_to_paid(self, is_klarna_payment=False):
        self.paid_at = timezone.now()
        # Save currency to avoid recalculating old orders
        # when currency for a region changes.
        self.currency = self.get_region().currency

        self.success_payment_received(is_klarna_payment=is_klarna_payment)
        self.update_waiting_list_entry()
        self.emit_purchase_related_events()
        metrics_client().increment('backend.order.placed', 1)
        metrics_client().increment('backend.order.settled', 1)
        metrics_client().increment('backend.order.paid', 1)
        metrics_client().increment(
            f'backend.{self.chosen_payment_method}.paymentmethod.success', 1
        )
        metrics_client().increment(
            'backend.order.paid.amount', self.get_base_total_value()
        )
        self.save(update_fields=['currency', 'paid_at'])

    def update_to_paid_after_normal_invoice_from_proforma_created(self, sell_date):
        self.paid_at = sell_date
        self.save()
        in_production = self.order_or_product_in_status_production_or_above()
        self.move_to_production_service.move()
        return in_production

    def is_business_order(self) -> bool:
        is_business_coupon = self.promo_text and any(
            [
                self.promo_text.lower().startswith(prefix)
                for prefix in Voucher.BUSINESS_VOUCHER_PREFIXES
            ]
        )
        return bool(is_business_coupon) or bool(self.vat) or self.vat_type == VAT_EU

    def _update_marketing_exclude_attributes(self) -> None:
        """Temporarily exclude new furniture owners from email marketing"""

        statuses_to_exclude = (
            OrderStatus.IN_PRODUCTION,
            OrderStatus.SHIPPED,
            OrderStatus.DELIVERED,
            OrderStatus.TO_BE_SHIPPED,
            OrderStatus.PAYMENT_PENDING,
        )
        if self.status in statuses_to_exclude and not self.contains_only_samples:
            delivery_date = None
            complaint_delivery_date = complaint_active = None
            today = timezone.now().date().isoformat()
            if self.status == OrderStatus.DELIVERED:
                delivery_date = today
                if self.order_type == OrderType.COMPLAINT:
                    complaint_delivery_date = today
                    complaint_active = False

            MarketingExcludeUpdateEvent(
                user=self.owner,
                last_furniture_order_status=self.get_status_display(),
                last_furniture_delivery_date=delivery_date,
                complaint_active=complaint_active,
                last_finished_complaint_date=complaint_delivery_date,
            )

    def update_if_strikethrough_promo(self):
        # to avoid circular import
        from promotions.utils import strikethrough_promo

        global_promo = strikethrough_promo()
        region_promo = strikethrough_promo(region=self.region)
        has_items = self.items.exists()

        if region_promo and not self.promo_text and has_items:
            # Add strikethrough promo if not added.
            self.promo_text = region_promo.promo_code.code
            self.used_promo = region_promo.promo_code

        elif (
            global_promo
            and not region_promo
            and self.promo_text == global_promo.promo_code.code
            and has_items
        ):
            # Remove strikethrough promo if promo is not available in that region.
            self.promo_text = ''
            self.used_promo = None
        else:
            return

        OrderPriceCalculator(self).calculate()

    def emit_purchase_related_events(self) -> None:
        """Update purchase related user attributes in Braze."""
        has_samples = self.items.filter(content_type__model='samplebox').exists()
        has_furniture = self.items.filter(
            content_type__model__in=['jetty', 'watty']
        ).exists()
        paid_at = self.paid_at.isoformat(timespec='minutes')

        PurchaseEvent(user=self.owner, email=self.email, order=self)
        PurchaseAttributesUpdateEvent(
            user=self.owner,
            last_furniture_purchase_date=paid_at if has_furniture else None,
            last_sample_purchase_date=paid_at if has_samples else None,
        )

    def is_assembly_service_mix(self):
        # TODO: return self.with_assembly
        furniture_items = self.items.exclude(
            content_type=ContentType.objects.get(model='samplebox')
        )
        assembly_services = [order_item.is_assembly() for order_item in furniture_items]
        return len(set(assembly_services)) > 1

    def emit_payment_related_events(self) -> None:
        PaymentEntryEvent(
            user=self.owner,
            user_id=self.owner.id,
            order_id=self.id,
            email=self.email,
            is_sample_order=self.contains_only_samples,
        )

    def emit_product_passport_ready_event(self) -> None:
        """Check if all assembly manuals ready and emit ProductPassportReadyEvent.

        Note: for now Product Passport is sent only for Jetty items.
        """
        if self.order_type == OrderType.COMPLAINT:
            return

        if Event.objects.filter(
            event_name=BrazeEventClasses.PRODUCT_PASSPORT_READY,
            properties__order_id=self.id,
        ).exists():
            return

        queryset = self.items.filter(
            content_type__model=Furniture.jetty.value
        ).prefetch_related(
            'order_item',
            'product_set',
            'product_set__product_details_jetty',
        )
        if not queryset:
            return

        for item in queryset:
            product = item.product_set.exclude(status=ProductStatus.ABORTED).last()
            if not (product and product.details.instruction):
                return

        ProductPassportReadyEvent(user=self.owner, email=self.email, order_id=self.id)

    @property
    def estimated_delivery_date(self):
        return self.estimated_delivery_time + timedelta(
            days=settings.EDD_TIME_ADJUSTMENT_DAYS
        )

    @property
    def has_pending_correction_request(self):
        from customer_service.models import CSCorrectionRequest

        return CSCorrectionRequest.objects.filter(
            invoice__order=self,
            status=CSCorrectionRequestStatus.STATUS_NEW,
        ).exists()

    def correction_requests(self):
        from customer_service.models import CSCorrectionRequest

        return CSCorrectionRequest.objects.filter(invoice__order=self).order_by('-id')

    def klarna_adjustments(self):
        from customer_service.models import KlarnaAdjustment

        return KlarnaAdjustment.objects.filter(invoice__order=self).order_by('-id')


class PaidOrders(Order):
    objects = PaidOrderManager()

    class Meta(object):
        proxy = True

    def referal(self):
        if self.owner and self.owner.profile.registration_referrer_uri:
            referal_string_builder = []
            for rs in [self.owner.profile.registration_referrer_uri]:
                if rs:
                    referal_string_builder.append(rs)
            return ' '.join(referal_string_builder)
        return '-'


class VIPOrderManager(models.Manager):
    def get_queryset(self):
        from producers.models import Product

        orders = Product.objects.filter(source_priority=SourcePriority.VIP).values_list(
            'order__id', flat=True
        )
        return super(VIPOrderManager, self).get_queryset().filter(id__in=orders)


class VIPOrder(Order):
    objects = VIPOrderManager()

    class Meta(object):
        proxy = True
        verbose_name = 'VIP order'
        verbose_name_plural = 'VIP orders'


class CustomOrder(Order):
    objects = CustomOrderManager()

    class Meta(object):
        proxy = True

    def invoice_id(self):
        return self.get_invoice_id()

    invoice_id.short_description = 'Invoice ID'

    def invoice(self):
        if self.invoice_set.exists():
            invoice = self.invoice_set.first()
            invoice_type = dict(InvoiceStatus.choices)[invoice.status]
            return safe(
                f"<a href='/admin/invoice/invoice/{invoice.id}' "
                f"target='_blank'>{invoice.pretty_id} {invoice_type}</a>",
            )
        return '-'

    invoice.short_description = 'Invoice'

    def items_in_production(self):
        if self.product_set.exists():
            return safe(
                ', '.join(
                    [
                        f"<a href='/admin/producers/product/{p.id}' "
                        f"target='_blank'>{str(p)}</a>"
                        for p in self.product_set.filter(
                            status=ProductStatus.IN_PRODUCTION
                        )
                    ]
                )
            )
        return ''


class VoucheredOrder(Order):
    objects = VoucheredOrderManager()

    class Meta(object):
        proxy = True


class OfflineReferralOrder(VoucheredOrder):
    class Meta(object):
        proxy = True

    objects = OfflineReferralOrderManager()


class PresentOrderItemsManager(SafeDeleteManager):
    """Exclude some old, non existent models which are still in GenericFields data."""

    def get_queryset(self):
        return (
            super().get_queryset().exclude(content_type__model__in=['table', 'grinder'])
        )


class OrderItem(
    Timestampable,
    RegionalizedMixin,
    SellableItemMixin,
    AggregateItemPriceMixin,
    SafeDeleteModel,
    ItemPriceAbstractModel,
):
    order = models.ForeignKey(
        Order,
        related_name='items',
        on_delete=models.CASCADE,
    )
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='samplebox')
            | models.Q(app_label='gallery', model='watty')
            | models.Q(app_label='gallery', model='sotty')
            | models.Q(app_label='services', model='additionalservice')
        ),
        on_delete=models.CASCADE,
    )
    object_id = models.PositiveIntegerField()
    order_item = GenericForeignKey('content_type', 'object_id')

    product_name = models.CharField(max_length=150, blank=True, null=True)
    invoice_product_name = models.CharField(max_length=150, blank=True, null=True)
    quantity = models.PositiveSmallIntegerField(default=1)
    with_assembly = models.BooleanField(
        default=False,
        verbose_name='assembly_enabled',
        help_text='Did client choose the assembly service or is it required',
    )
    free_return = models.ForeignKey(
        'free_returns.FreeReturn',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    # TODO: add new model ExportToBigQuery with fields model, datetime, table
    # in use for samples export to BigQuery
    exported_to_big_query = models.DateTimeField(null=True, blank=True, default=None)

    objects = PresentOrderItemsManager()

    class Meta:
        unique_together = ['id', 'free_return']

    def __str__(self):
        return (
            f'Order item ({self.id}) - {self.get_furniture_type()} '
            f'({self.get_furniture_id()}) for order: {self.order_id}'
        )

    @property
    def sellable_item(self) -> 'SellableItemAbstract':
        return self.order_item

    @property
    def parent(self) -> 'Order':
        return self.order

    @property
    def fields_dict(self) -> dict:
        pricing_dict = self.common_fields
        return pricing_dict | {
            'quantity': self.quantity,
            'with_assembly': self.with_assembly,
        }

    def change_region(self, new_region):
        self.clear_methods_cache()
        self.region = new_region
        self.save(update_fields=['region'])

    def get_invoice_item_net_value(self):
        return self.invoiceitem_set.aggregate(Sum('net_value'))['net_value__sum']

    @property
    def adyen_price(self) -> int:
        """
        Adyen needs prices in minor monetary unit.
        If we introduce more currencies check if they minor unit is as well a 100
        times smaller than the major.
        Adyen docs: https://docs.adyen.com/development-resources/currency-codes
        """
        return int(self.get_price_number() * 100)

    @property
    def adyen_tax_rate(self) -> int:
        """Adyen wants tax rate in "minor units", which means percentage * 100"""
        vat_queryset = self.region.countries.values_list('vat', flat=True)
        if vat_queryset.count() > 1:
            logger.debug(
                f'Order item {self.id} has many VAT values. This should never happened'
            )
        return int(vat_queryset.first() * 100 * 100)

    def serialize_furniture(self):
        try:
            furniture_serializer = furniture_serializer_class_factory(self.order_item)
            serialized_furniture = furniture_serializer(self.order_item).data
        except Exception:
            logger.exception(
                add_order_context(
                    'Error while serializing %s' % self, order=self.order
                ),
                extra={'order_id': self.order.id},
            )
            serialized_furniture = None
        return serialized_furniture

    def get_product(self):
        return self.product_set.exclude(status=ProductStatus.ABORTED).last()

    def get_logistic_order(self):
        if self.order_item.product_type == Furniture.sample_box.value:
            return self.order.get_logistic_order_for_sample_box()

        product = self.get_product()
        if product is None:
            return None
        return self.order.get_logistic_order_by_id(product.logistic_order)

    def get_furniture_type(self):
        if self.order_item is None:
            return 'None'
        elif self.is_service:
            return str(self.order_item)
        return self.order_item.get_furniture_type()

    def get_furniture_id(self):
        if self.order_item is not None:
            return self.order_item.id
        return '-'

    def product_is_batched(self):
        return any([x.batch_id is not None for x in self.product_set.all()])

    def clear_prices(self):
        self.price = 0
        self.region_price = 0

        self.price_net = 0
        self.region_price_net = 0

        self.assembly_price = 0
        self.region_assembly_price = 0

        self.delivery_price = 0
        self.region_delivery_price = 0

        self.vat_amount = 0
        self.region_vat_amount = 0

        self.region_promo_value = 0

    def is_source_for_switch(self):
        return getattr(self, 'order_source', None)

    def is_target_for_switch(self):
        return getattr(self, 'order_target', None)

    @cached_property
    def delivery_range(self):
        if not self.order.estimated_delivery_time:
            return None, None
        offset = 7 if not self.is_assembly() else 14
        return calculate_calendar_week_range(self.order.estimated_delivery_time, offset)

    def is_assembly(self):
        # TODO: return self.with_assembly
        if (
            self.order_item.get_furniture_type() == Furniture.watty.value
            and self.order_item.shelf_type == ShelfType.TYPE03.value
        ):
            return True
        return self.order.assembly

    def is_samplebox(self):
        return self.order_item.furniture_type == Furniture.sample_box.value

    def is_jetty(self):
        return self.order_item.furniture_type == Furniture.jetty.value

    def is_watty(self):
        return self.order_item.furniture_type == Furniture.watty.value

    def is_sotty(self):
        return self.order_item.furniture_type == Furniture.sotty.value


# TODO: Remove after carts are moved to separate model
class CartManager(models.Manager):
    def get_queryset(self):
        return (
            super(CartManager, self)
            .get_queryset()
            .filter(
                status=OrderStatus.CART,
            )
            .prefetch_related('owner__profile', 'items__order_item')
        )


class CartOrders(Order):
    objects = CartManager()

    class Meta(object):
        proxy = True
        verbose_name = 'Cart'
        verbose_name_plural = 'Carts'


class PaidOrdersGeo(models.Model):
    order = models.OneToOneField(PaidOrders, on_delete=models.CASCADE)
    geo_city = models.ForeignKey(GeoCity, null=True, on_delete=models.CASCADE)

    @staticmethod
    def find_geodatas(curr_year=2017):
        if not curr_year:
            curr_year = date.today().year
        orders = (
            PaidOrders.objects.filter(
                city__isnull=False,
                paid_at__gte='{}-01-01'.format(curr_year),
                paidordersgeo__isnull=True,
            )
            .exclude(region__name=OTHER_REGION_NAME)
            .order_by('-id')
        )
        for order in orders:
            # NOTE: temporary solution as long as a KPI and a file on google drive
            #  using PaidOrdersGeo needs it in this form, but we don't want
            #  a dependency on the geocoder package anymore
            # TODO: Delete the entire model when neither the KPI
            #  nor the file is needed anymore
            PaidOrdersGeo(geo_city=None, order=order).save()


class OrderStatusHistory(models.Model):
    order = models.ForeignKey(
        Order,
        related_name='status_history',
        on_delete=models.CASCADE,
    )
    status = models.IntegerField(choices=OrderStatus.choices)
    previous_status = models.IntegerField(choices=OrderStatus.choices)
    description = models.TextField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta(object):
        verbose_name = 'Order status history'
        verbose_name_plural = 'Orders status history'
        ordering = ('created_at',)

    def __str__(self):
        return (
            f'Status change for {self.order_id} from '
            f'{self.get_previous_status_display()} to '
            f'{self.get_status_display()} - {self.created_at}'
        )


class OrderStatusCheckHistory(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    order = models.CharField(blank=True, max_length=64)
    postal_code = models.CharField(blank=True, max_length=32)
    email = models.EmailField(blank=True)
    order_status = models.IntegerField(null=True)
    success = models.BooleanField(default=False)


class OrderToProduction(models.Model):
    original_order = models.OneToOneField(Order, on_delete=models.SET_NULL, null=True)
    process_at = models.DateTimeField(null=True, blank=True)
    is_klarna_payment = models.BooleanField(default=False)
    errors = models.JSONField(blank=True, default=list)

    class Meta:
        ordering = ('-id',)
        verbose_name_plural = 'Orders to Production'


class OrderToProductionInvalid(OrderToProduction):
    objects = OrderToProductionInvalidManager()

    class Meta:
        proxy = True
        ordering = ('-id',)
        verbose_name_plural = 'Orders to Production Invalid'


class OrderBarterData(models.Model):
    """
    Used when order is paid fully or partially with a barter deal.
    """

    order = models.OneToOneField(
        Order,
        on_delete=models.CASCADE,
        related_name='barter_data',
    )
    barter_deal = models.ForeignKey(
        'vouchers.VoucherBarterDeal',
        on_delete=models.PROTECT,
        related_name='order_data',
    )

    class Meta:
        verbose_name = 'Order Barter Data'
        verbose_name_plural = 'Orders Barter Data'

    def handle_region_change(self, new_region: Region) -> None:
        if new_region.currency != self.barter_deal.currency:
            self.delete()

    def get_order_value_with_barter_extracted(self):
        if self.barter_deal.currency != self.order.region.currency:
            raise ValueError(
                f'Currency mismatch for order {self.order.id}, '
                f'voucher {self.barter_deal.voucher.code}'
            )
        return self.order.region_total_price - self.barter_deal.value


class MonthTarget(models.Model):
    sample_boxes = models.PositiveIntegerField(default=0, help_text='Sold sample boxes')
    sample_orders = models.PositiveIntegerField(
        default=0, help_text='Orders with sample boxes'
    )
