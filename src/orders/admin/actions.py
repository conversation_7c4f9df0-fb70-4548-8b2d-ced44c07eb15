import logging

from django.contrib import (
    admin,
    messages,
)
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from django.core.paginator import Paginator
from django.http import HttpResponseRedirect
from django.shortcuts import (
    redirect,
    render,
)
from django.urls import reverse
from django.utils import timezone

from carts.services.cart_service import CartService
from checkout.klarna_capture import (
    ValidateKlarnaOrderException,
    send_klarna_capture_for_order,
)
from custom.admin_action import admin_action_with_form
from custom.admin_permissions import group_member_required
from custom.enums.enums import AdminGroup
from custom.utils.decorators import (
    CSVObject,
    csv_export,
)
from invoice.models import (
    Invoice,
    InvoiceDomestic,
)
from orders.enums import OrderStatus
from orders.forms import (
    EditOrderNotesForm,
    SendOrderNotificationsAdminMiddleViewForm,
)
from orders.internal_api.events import (
    OrderPushedToProductionEvent,
    OrderRefreshEvent,
)
from pricing_v3.services.price_calculators import (
    OrderPriceCalculatorForPriceUpdatedAtPricing,
)

logger = logging.getLogger('orders')


@csv_export
@admin.action(description='Export selected orders to csv')
def export_orders_as_csv(modeladmin, request, queryset):
    header_row = [
        'order_id',
        'paid_at',
        'user_id',
        'user_username',
        'user_email',
        'user_language',
        'customer_data',
        'total_price',
        'sell_channel',
        'registration_source',
        'order_source',
        'user_date_joined',
        'lead_time',
        'first_buy_date',
        'total_amount',
        'promo_code',
        'promo_amount',
        'sent_to_customer',
        'delivered_to_customer',
        'tracking_number',
    ]
    address_header_row, rows = [], []
    for page in Paginator(queryset, 100):
        for o in page.object_list:
            lo = o.logistic_info[-1]
            address_data = o.get_all_address_data()
            if len(address_header_row) == 0:
                address_header_row = list(address_data.keys())
            row = [
                o.id,
                o.paid_at,
                o.owner.id,
                o.owner.username,
                o.email,
                o.owner.profile.language,
                o.get_customer_as_string().replace('<br/>', ''),
                o.get_total_price(),
                o.get_sell_channel(),
                o.get_order_source_display(),
                o.owner.profile.get_registration_source_display(),
                o.owner.date_joined,
                o.lead_time,
                o.owner.profile.first_buy_date,
                o.get_base_total_value(),
                o.promo_text,
                o.get_base_promo_amount(),
            ]
            if lo is not None:
                row += [lo.sent_to_customer, lo.delivered_date, lo.tracking_number]
            else:
                row.extend(['', '', ''])
            for ah in address_header_row:
                row.append(address_data[ah])
            rows.append(row)
    return CSVObject(
        file_name='order_export',
        header_row=header_row + address_header_row,
        rows=rows,
    )


@admin.action(description='Create proforma invoice from this order')
@group_member_required(AdminGroup.ACCOUNTING)
def create_proforma_invoice(modeladmin, request, queryset):
    for order in queryset:
        order.create_proforma_invoice()


@admin.action(description='Create invoice from this order')
@group_member_required(AdminGroup.ACCOUNTING)
def create_invoice(modeladmin, request, queryset):
    for order in queryset:
        if order.payable_booking_date is not None:
            sell_at = order.payable_booking_date
        elif order.settled_at is not None:
            sell_at = order.settled_at
        elif order.paid_at is not None:
            sell_at = order.paid_at
        elif order.placed_at is not None:
            sell_at = order.placed_at
        else:
            sell_at = order.created_at
        invoice = Invoice.objects.create(
            order=order,
            sell_at=sell_at,
            issued_at=timezone.now(),
            currency_symbol=order.get_region().currency.symbol,
        )
        if order.is_united_kingdom_lte_tax_threshold(order.region_total_price):
            invoice.set_additional_total_when_united_kingdom_lte_tax_threshold(
                order.region_total_price
            )
        invoice.create_pdf()
        if invoice.order.earliest_invoice_domestic_version_supported():
            invoice_domestic = (
                InvoiceDomestic.objects.create_domestic_from_normal_or_proforma(invoice)
            )
            invoice_domestic.create_pdf()


@admin.action(description='Create logistic order from this order')
def create_logistic_order_from_order(modeladmin, request, orders):
    for order in orders:
        OrderPushedToProductionEvent(order)


def save_order_note_and_redirect(modeladmin, request, queryset, form):
    form.save()
    OrderRefreshEvent(form.instance)
    return redirect('admin:orders_order_changelist')


def save_order_vat_and_redirect(modeladmin, request, queryset, form, object_id):
    form.save()
    OrderRefreshEvent(form.instance)
    return redirect(reverse('admin:orders_order_change', args=(object_id,)))


@admin.action(description='Edit Order Note')
def update_order_notes(modeladmin, request, queryset):
    order = queryset.first()
    return admin_action_with_form(
        modeladmin=modeladmin,
        request=request,
        instance=order,
        queryset=queryset,
        form_class=EditOrderNotesForm,
        form_initial={
            'order_notes': order.order_notes,
        },
        success_function=save_order_note_and_redirect,
        success_function_kwargs={},
    )


@admin.action(description='Recalculate order')
def recalculate_order(modeladmin, request, queryset):
    for order in queryset:
        OrderPriceCalculatorForPriceUpdatedAtPricing(order=order).calculate()


@admin.action(
    description='Create production item from this order (using process_to_production)'
)
def create_production_item_from_order(modeladmin, request, queryset):
    for order in queryset:
        in_production = order.order_or_product_in_status_production_or_above()
        if in_production:
            modeladmin.message_user(request, in_production)
        order.move_to_production_service.move()


@admin.action(description='Capture klarna transactions and update proforma invoice')
def capture_klarna_transactions_and_update_invoice(self, request, queryset):
    """
    Capture send request to adyen. Confirmation that everything with payment is ok
    is sent via notification. If capture is confirmed, proforma invoice created
    earlier to given transaction is updated as normal
    """
    for order in queryset:
        try:
            send_klarna_capture_for_order(order)
            self.message_user(request, f'Capture was sent for Order {order.id}.')
        except ValidateKlarnaOrderException as e:
            self.message_user(
                request,
                f'Order {order.id} not send. Reason: {e}',
                level=messages.WARNING,
            )


@admin.action
def pay_for_order(self, request, queryset):
    for order in queryset:
        if order.status in [OrderStatus.PAYMENT_PENDING, OrderStatus.CART]:
            order.paid_at = timezone.now()
            order.move_to_production_service.move()
            order.change_products_to_ordered()
            CartService(order.cart).handle_paid_order()
            if not order.order_pretty_id:
                order.create_pretty_id(suborders_count=0)

            order.save()


@admin.action(description='Unify email data and send order notifications')
def unify_email_and_send_order_notifications(self, request, queryset):
    form = None
    if queryset.count() > 1:
        return
    order = queryset.first()
    if 'apply' in request.POST:
        form = SendOrderNotificationsAdminMiddleViewForm(request.POST)
        if form.is_valid():
            order.send_order_notifications(
                email_address=form.cleaned_data['email_address'],
                order_confirmation=form.cleaned_data['order_confirmation'],
                payment_confirmation=form.cleaned_data['payment_confirmation'],
                invoice_email=form.cleaned_data['invoice_email'],
            )
            messages.info(request, 'Email address set and notifications sent')
        return HttpResponseRedirect(request.get_full_path())
    if not form:
        form = SendOrderNotificationsAdminMiddleViewForm(
            initial={
                'email_address': order.email,
                '_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME),
            }
        )
    opts = self.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/unify_email_form.html',
        {'form': form, 'opts': opts, 'app_label': app_label},
    )
