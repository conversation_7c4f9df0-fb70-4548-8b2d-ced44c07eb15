import string

from django.db.models.signals import post_save
from django.utils import timezone

import factory

from factory import fuzzy

from payments.choices import KlarnaStatus
from payments.models import KlarnaCaptureRequest


@factory.django.mute_signals(post_save)
class TransactionFactory(factory.django.DjangoModelFactory):
    order = factory.SubFactory(
        'orders.tests.factories.OrderFactory',
    )
    amount = factory.SelfAttribute('.order.total_price')
    reference = fuzzy.FuzzyText(length=16, chars=string.digits)
    merchant_reference = factory.Sequence(
        (lambda counter: f'Merchant reference {counter}'),
    )
    status = fuzzy.FuzzyChoice(
        choices=[
            'AUTHORISATION',
            'AUTHORISED',
            'CANCELLED',
            'PENDING',
            'REFUSED',
        ],
    )
    live = True

    class Meta:
        model = 'payments.Transaction'


class NotificationFactory(factory.django.DjangoModelFactory):
    code = fuzzy.FuzzyChoice(
        choices=[
            'AUTHORISATION',
            'CANCELLATION',
            'REFUND',
            'CANCEL_OR_REFUND',
            'CAPTURE',
            'REFUNDED_REVERSED',
            'CAPTURE_FAILED',
            'REFUND_FAILED',
            'REQUEST_FOR_INFORMATION',
            'NOTIFICATION_OF_CHARGEBACK',
            'ADVICE_OF_DEBIT',
            'CHARGEBACK',
            'CHARGEBACK_REVERSED',
            'REPORT_AVAILABLE',
        ]
    )
    event_date = timezone.now()

    class Meta:
        model = 'payments.Notification'


class KlarnaCaptureRequestFactory(factory.django.DjangoModelFactory):

    psp_reference = fuzzy.FuzzyText(length=16, chars=string.digits)
    status = fuzzy.FuzzyChoice(v for v, _ in KlarnaStatus.choices)
    transaction = factory.SubFactory(TransactionFactory)
    error_message = fuzzy.FuzzyText(length=64)

    class Meta:
        model = KlarnaCaptureRequest
