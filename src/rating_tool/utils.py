import csv
import functools
import json
import pathlib

from typing import (
    Iterable,
    Optional,
)

from django.templatetags.static import static
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

from custom.enums import Furniture
from gallery.enums import (
    FurnitureCategory,
    FurnitureImageType,
)
from promotions.services.categories_in_promo import get_categories_in_promo
from rating_tool.constants import OLD_TO_NEW_GRID_CATEGORY_NAME_MAP
from rating_tool.enums import (
    BoardCategoryEnum,
    FurnitureTypeColumn,
)
from rating_tool.models import (
    FURNITURE_MODELS,
    Board,
    BoardCategory,
)


class IncorrectBoardOrderRowError(Exception):
    pass


class WrongCategoryError(Exception):
    """Raised when category provided by the user doesn't much category from the file"""


def read_category_images():
    current_dir = pathlib.Path(__file__).parent.absolute()

    with open(f'{current_dir}/media/category_images.json', 'r') as fh:
        return json.load(fh)


@functools.lru_cache(maxsize=1)
def get_categories(request):
    category_images = read_category_images()
    categories_in_promotion = {
        category['categoryName']: category['value']
        for category in get_categories_in_promo()
    }
    return [
        {
            'name': _('martin_common_all_furniture'),
            'description': _('martin_category_description_all_shelves'),
            'img_jpg': static('header-optimized/allshelves_mobile.jpg'),
            'img_webp': static('header-optimized/allshelves_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_all_furniture'),
            'cat_number': '',
            'url': reverse_lazy('front-products-list-shelf'),
        },
        {
            'name': _('martin_common_sideboard_plural'),
            'name_single': _('martin_common_sideboard_singular'),
            'img_jpg': static('header-optimized/sideboard_mobile.jpg'),
            'img_webp': static('header-optimized/sideboard_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_sideboard_plural'),
            'description': _('martin_category_description_sideboard'),
            'cat_number': 'cat_3',
            'category': 'sideboard',
            'url': reverse_lazy('front-products-list-shelf', args=['sideboard']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.SIDEBOARD,
                False,
            ),
        },
        {
            'name': _('martin_common_bookcase_plural'),
            'name_single': _('martin_common_bookcase_singular'),
            'img_jpg': static('header-optimized/bookshelf_mobile.jpg'),
            'img_webp': static('header-optimized/bookshelf_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_bookcase_plural'),
            'description': _('martin_category_description_bookcase'),
            'cat_number': 'cat_1',
            'category': 'bookcase',
            'url': reverse_lazy('front-products-list-shelf', args=['bookcase']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.BOOKCASE,
                False,
            ),
        },
        {
            'name': _('martin_common_wall_storage_plural'),
            'name_single': _('martin_common_wall_storage_singular'),
            'img_jpg': static('header-optimized/wallstorage_plus_mobile.jpg'),
            'img_webp': static('header-optimized/wallstorage_plus_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_wall_storage_plural'),
            'description': _('martin_category_description_wall_storage'),
            'cat_number': 'cat_5',
            'category': 'wallstorage',
            'url': reverse_lazy('front-products-list-shelf', args=['wallstorage']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.WALL_STORAGE,
                False,
            ),
        },
        {
            'name': _('martin_common_wardrobe_plural'),
            'name_nobr': _('martin_common_wardrobe_plural_nobr'),
            'name_single': _('martin_common_wardrobe_singular'),
            'img_jpg': static('header-optimized/wardrobe_mobile.jpg'),
            'img_webp': static('header-optimized/wardrobe_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_wardrobe_plural'),
            'description': _('martin_category_description_wardrobe'),
            'cat_number': 'cat_8',
            'category': 'wardrobe',
            'is_new': True,
            'url': reverse_lazy('front-products-list-shelf', args=['wardrobe']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.WARDROBE,
                False,
            ),
        },
        {
            'name': _('martin_common_desk_plural'),
            'name_nobr': _('martin_common_desk_plural'),
            'name_single': _('martin_common_desk_singular'),
            'img_jpg': static('header-optimized/<EMAIL>'),
            'img_webp': static('header-optimized/<EMAIL>'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_desk_plural'),
            'description': _('martin_category_description_desk'),
            'cat_number': 'cat_10',
            'category': 'desk',
            'is_new': True,
            'url': reverse_lazy('front-products-list-shelf', args=['desk']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.DESK,
                False,
            ),
        },
        {
            'name': _('martin_common_tv_stand_plural'),
            'name_single': _('martin_common_tv_stand_singular'),
            'img_jpg': static('header-optimized/tvstand_mobile.jpg'),
            'img_webp': static('header-optimized/tvstand_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_tv_stand_plural'),
            'description': _('martin_category_description_tv_stand'),
            'cat_number': 'cat_4',
            'category': 'tv-stand',
            'category1': 'tvstand',
            'url': reverse_lazy('front-products-list-shelf', args=['tv-stand']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.TV_STAND,
                False,
            ),
        },
        {
            'name': _('martin_common_chest_drawers_plural'),
            'name_single': _('martin_common_chest_drawers_singular'),
            'img_jpg': static('header-optimized/chestofdrawers_mobile.jpg'),
            'img_webp': static('header-optimized/chestofdrawers_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_chest_drawers_plural'),
            'description': _('martin_category_description_chest_drawers'),
            'cat_number': 'cat_6',
            'category': 'chest-of-drawers',
            'category1': 'chest',
            'url': reverse_lazy('front-products-list-shelf', args=['chest-of-drawers']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.CHEST.value,
                False,
            ),
        },
        {
            'name': _('martin_common_shoe_rack_plural'),
            'name_single': _('martin_common_shoe_rack_singular'),
            'img_jpg': static('header-optimized/shoerack_mobile.jpg'),
            'img_webp': static('header-optimized/shoerack_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_shoe_rack_plural'),
            'description': _('martin_category_description_shoe_rack'),
            'cat_number': 'cat_2',
            'category': 'shoerack',
            'url': reverse_lazy('front-products-list-shelf', args=['shoerack']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.SHOERACK.value,
                False,
            ),
        },
        {
            'name': _('martin_common_vinyl_storage_plural'),
            'name_nobr': _('martin_common_vinyl_storage_plural_nobr'),
            'name_single': _('martin_common_vinyl_storage_singular'),
            'img_jpg': static('header-optimized/vinyl_mobile.jpg'),
            'img_webp': static('header-optimized/vinyl_mobile.webp'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_vinyl_storage_plural'),
            'description': _('martin_category_description_vinyl_storage'),
            'cat_number': 'cat_7',
            'category': 'vinyl_storage',
            'category1': 'vinyl-storage',
            'url': reverse_lazy('front-products-list-shelf', args=['vinyl_storage']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.VINYL_STORAGE.value,
                False,
            ),
        },
        {
            'name': _('martin_common_bedside_plural'),
            'name_nobr': _('martin_common_bedside_plural'),
            'name_single': _('martin_common_bedside_singular'),
            'img_jpg': static('header-optimized/<EMAIL>'),
            'img_webp': static('header-optimized/<EMAIL>'),
            'img_jpg_x2': static('header-optimized/<EMAIL>'),
            'img_webp_x2': static('header-optimized/<EMAIL>'),
            'img_base64': category_images.get('martin_common_bedside_plural'),
            'description': _('martin_category_description_bedside'),
            'cat_number': 'cat_9',
            'category': 'bedside_table',
            'category1': 'bedside-table',
            'url': reverse_lazy('front-products-list-shelf', args=['bedside_table']),
            'promo_value': categories_in_promotion.get(
                FurnitureCategory.BEDSIDE_TABLE.value,
                False,
            ),
        },
    ]


def get_additional_images_for_grid_object(item):
    return [
        {'url': image.url, 'webp': image.webp_url, 'color': image.color}
        for image in item.additional_images.all()
        if image.type == FurnitureImageType.INSTA_GRID_IMAGE
        and image.enabled is not False
    ]


def get_adjusted_boards_data_duplicate_for_new_grid(board_category):
    # first, easy change, just rename
    new_category_dict = {
        OLD_TO_NEW_GRID_CATEGORY_NAME_MAP.get(board_name): get_board_data_without_usps(
            board_data
        )
        for board_name, board_data in board_category.boards_data.items()
        if board_name in OLD_TO_NEW_GRID_CATEGORY_NAME_MAP
    }

    # then, harder ones:
    all_shelves_items = get_board_data_without_usps(
        board_category.boards_data['cat_all__type_all']
    )

    sorted_items = sorted(
        all_shelves_items, key=lambda item: item[3]['imp_item_status_width']
    )
    one_third = len(all_shelves_items) // 3

    narrow_items = sorted_items[:one_third]
    wide_items = sorted_items[len(sorted_items) - one_third - 1 :]

    new_category_dict['cat_narrow'] = [
        item for item in all_shelves_items if item in narrow_items
    ]
    new_category_dict['cat_wide'] = [
        item for item in all_shelves_items if item in wide_items
    ]

    return new_category_dict


def get_board_data_without_usps(board_data):
    return [item_data for item_data in board_data if item_data[2] != 'usp']


def create_new_category_from_data(board_category, new_boards_data, prefix='Grid 2.0'):
    category = board_category
    category.id = None
    category.pk = None
    category._state.adding = True
    category.name = f'{prefix} based on: {board_category.name}'[:40]
    category.boards_data = new_boards_data
    category.for_category_grid = False
    category.for_board_grid = False
    category.for_mini_grid = False
    category.enabled_for_webview = False
    category.parent = None
    category.description = f'{prefix} adjusted copy of {board_category.name}'
    category.save()


def get_used_in_boards_furniture_ids(furniture_type: str) -> set[int]:
    """Gets ids of jetties or watties that had been used in boards before"""
    board_categories = BoardCategory.objects.filter(
        enabled_for_webview=True,
        boards_data__isnull=False,
    )
    result_ids = set()
    for bc in board_categories:
        for key, board in bc.boards_data.items():
            for element in board:
                if element[2] == furniture_type and element[0] not in result_ids:
                    result_ids.add(element[0])
    return result_ids


def get_ids_to_be_copied(
    furniture_type: str,
    new_ids: Iterable[int],
    old_ids: Optional[Iterable[int]] = None,
) -> set[int]:
    """Gets ids to be copied for setting up the board

    If we wouldn't copy the id of the furniture used on an older board and add
    the same furniture to the new board, and then add a FurnitureImage to that
    furniture, the image would appear on both boards, which we want to avoid.
    """
    all_ids = get_used_in_boards_furniture_ids(furniture_type)
    ids_to_be_copied = set(new_ids) & all_ids
    if old_ids:
        ids_to_be_copied -= old_ids
    return ids_to_be_copied


def create_board_order_from_csv(
    csv_file,
    board_category_number: int = None,
    furniture_category: str = None,
) -> list:
    reader = csv.reader(csv_file)
    next(reader)
    row = next(reader)
    cat_number = row[14]

    if board_category_number and furniture_category:
        if (
            board_category_number != BoardCategoryEnum.MINIGRID.value
            and not cat_number.startswith(furniture_category)
        ):
            raise WrongCategoryError

    board_order = []
    for row in reader:
        try:
            parsed_row = parse_board_order_row(row)
            board_order.append(parsed_row)
        except IncorrectBoardOrderRowError:
            break
    return board_order


def parse_board_order_row(row: list) -> list:
    furniture_type_row = row[1]
    if not furniture_type_row:
        raise IncorrectBoardOrderRowError
    material = row[9]
    furniture_id = row[10]
    if furniture_type_row.startswith(FurnitureTypeColumn.USP.value):
        usp_number = row[8] or f'U{furniture_type_row[4:6]}'
        return [usp_number, -1, 'usp', {}]
    elif furniture_type_row == FurnitureTypeColumn.TYPE03.value:
        furniture_type = Furniture.watty.value
    else:
        furniture_type = Furniture.jetty.value
    return [int(furniture_id), int(material), furniture_type, {}]


def make_furniture_data_for_board_order(furniture) -> list:
    """Prepares the data needed to place the furniture in the board order"""
    return [
        furniture.id,
        furniture.material,
        furniture.furniture_type,
        furniture.additional_data_for_boards(),
    ]


def update_board_order_at_given_index(board: Board, index: int, furniture) -> None:
    furniture_data = make_furniture_data_for_board_order(furniture)
    board.order[index] = furniture_data
    board.save()


def get_furniture_or_duplicate(furniture):
    """In case the furniture was used in boards before it returns its duplicate"""
    if furniture.id in get_used_in_boards_furniture_ids(furniture.furniture_type):
        final_id = furniture.duplicate()
        furniture_model = FURNITURE_MODELS[furniture.furniture_type]
        furniture = furniture_model.objects.get(pk=final_id)
    return furniture


def replace_furniture(board: Board, old_id: int, new_furniture) -> int:
    """Replace furniture with given id with the new furniture in board order.

    If the new furniture was used in boards before, it is duplicated and the new
    duplicated furniture is placed in the board"""
    final_furniture = get_furniture_or_duplicate(new_furniture)
    final_furniture.set_as_preset()
    shelf_category = board.get_shelf_category()
    if shelf_category:
        final_furniture.shelf_category = shelf_category
        final_furniture.save()
    order_index = [x[0] for x in board.order].index(old_id)
    update_board_order_at_given_index(board, order_index, final_furniture)
    return final_furniture.id


def update_board_order_data(board: Board) -> None:
    """Updates board order with proper data.

    Board order from the given board is either incomplete or outdated, since it was
    either created from csv file or changed manually.
    """
    jetties_ids = board.get_furniture_ids(Furniture.jetty.value)
    watties_ids = board.get_furniture_ids(Furniture.watty.value)
    furniture_ids_to_be_copied = {
        Furniture.jetty.value: get_ids_to_be_copied(Furniture.jetty.value, jetties_ids),
        Furniture.watty.value: get_ids_to_be_copied(Furniture.watty.value, watties_ids),
    }
    board_shelf_category = board.get_shelf_category()
    new_board_order = []
    for i, item_data in enumerate(board.order):
        new_item_data = process_board_order_item(
            item_data,
            board_shelf_category,
            furniture_ids_to_be_copied,
        )
        new_board_order.append(new_item_data)
    board.order = new_board_order
    board.save()


def process_board_order_item(
    item_data: list,
    board_shelf_category: str,
    furniture_ids_to_be_copied: dict[str, set[int]],
) -> list:
    """Processes single item in board data.

    It makes sure the piece of furniture is duplicated and shelf category is updated
    when it is necessary.
    Returns updated data for single item"""
    furniture_type = item_data[2]
    if furniture_type == 'usp':
        return item_data
    furniture_id = item_data[0]
    furniture = FURNITURE_MODELS[furniture_type].objects.get(pk=furniture_id)
    if furniture_id in furniture_ids_to_be_copied[furniture_type]:
        new_id = furniture.duplicate()
        furniture = FURNITURE_MODELS[furniture_type].objects.get(pk=new_id)
        item_data[0] = new_id
    furniture.set_as_preset()
    if board_shelf_category:
        furniture.shelf_category = board_shelf_category
        furniture.save()
    item_data[3] = furniture.additional_data_for_boards()
    return item_data


def update_board_order_additional_data(board) -> None:
    """Updates additional data of items in board_order."""
    new_order = []
    for item_data in board.order:
        furniture_type = item_data[2]
        if furniture_type == 'usp':
            new_order.append(item_data)
            continue
        furniture_id = item_data[0]
        furniture = FURNITURE_MODELS[furniture_type].objects.get(pk=furniture_id)
        item_data[3] = furniture.additional_data_for_boards()
        new_order.append(item_data)
    board.order = new_order
    board.save()
