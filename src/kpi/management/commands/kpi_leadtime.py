import datetime

from django.core.management.base import BaseCommand
from django.core.paginator import Paginator

import numpy as np

from custom.internal_api.deserializers import LogisticOrderLeadTimeDTODeserializer
from custom.internal_api.dto import LogisticOrderLeadTimeDTO
from kpi.big_query import (
    bigquery,
    upload_list_to_big_query,
)
from orders.enums import OrderType
from orders.internal_api.clients import LogisticOrderAPIClient
from orders.models import Order
from producers.choices import ProductStatus

DATA_SPACE = 'kpi'
DATA_TABLE = 'lead_time'
DT = ['DHL', 'UPS', 'DPD', 'TNT', 'FEDEX']


class Command(BaseCommand):
    def count_days(self, start_day, end_day):
        return np.busday_count(start_day, end_day, weekmask='1111111')

    def calculate(self, start_date):
        orders = self.get_data(start_date)

        logistic_order_api_client = LogisticOrderAPIClient()

        all_data = []
        for page in Paginator(orders, 500):
            orders_per_page = page.object_list

            logistic_orders_by_order = (
                logistic_order_api_client.grouped_by_order_for_leadtime(orders_per_page)
            )
            for order in orders_per_page:
                self.filter_data(logistic_orders_by_order, order)
                mix = True if len(self.logistic_type) > 1 else False
                sample_box_in_logistics = 'sample_box' in self.logistic_type
                sample_box = len(self.logistic_type) == 1 and sample_box_in_logistics
                assembly = (
                    True
                    if order.assembly or 'W3' == self.products_shelf_type
                    else False
                )
                len_items = order.items.count()

                if not self.logistic_info_all:
                    continue

                logistic_time = [
                    logistic_order.lead_time
                    for logistic_order in self.logistic_info_all
                    if logistic_order.order_type not in ['sample_box', 'watty']
                ]

                if not sample_box and (
                    not self.operations_product_produced_date_by_prod_date
                    or not self.operations_product_produced_date_by_prod_date[0]
                    or not self.operations_product_produced_date_by_prod_date[0][0]
                ):
                    print('no production history?')
                    continue
                carrier = (
                    self.logistic_info_all[0].carrier if self.logistic_info_all else ''
                )
                if len_items == 1 and not sample_box_in_logistics:
                    (
                        logistic_time,
                        operation_time_t1,
                        operation_time_t2,
                        production_time,
                        production_time_diff,
                        total_time_full,
                        logistic_operation_time,
                    ) = self.one_item_order_without_sample_box(logistic_time, order)

                elif sample_box:
                    production_time = 0
                    production_time_diff = 0
                    operation_time_t1 = 0
                    operation_time_t2 = self.count_days(
                        self.logistic_info_all[0].sent_to_customer,
                        self.logistic_info_all[0].delivered_date,
                    )
                    total_time_full = self.count_days(
                        order.paid_at.date(),
                        self.logistic_info_all[0].delivered_date,
                    )
                    self.manufactures = ''
                    self.logistic_type = 'sample_box'
                    self.products_shelf_type = 'sample_box'
                    logistic_time = 0
                    logistic_operation_time = operation_time_t2
                elif not sample_box_in_logistics:
                    (
                        carrier,
                        logistic_time,
                        operation_time_t1,
                        operation_time_t2,
                        production_time,
                        production_time_diff,
                        total_time_full,
                        logistic_operation_time,
                    ) = self.multi_item_orders(order)
                else:
                    continue

                delivered = self.logistic_info_all[0].delivered_date
                if carrier in DT:
                    carrier_type = 'Courier'
                elif carrier == 'Mix':
                    carrier_type = 'Mix'
                else:
                    carrier_type = 'DT'

                data = dict(
                    [
                        ('id', order.id),
                        (
                            'delivered_at',
                            delivered.replace(day=1).strftime('%Y-%m-%d')
                            if delivered
                            else '',
                        ),
                        ('assembly', assembly),
                        ('items', len_items),
                        ('many_items', True if len_items != 1 else False),
                        ('items_types', self.logistic_type),
                        (
                            'delivered_all',
                            all(
                                [
                                    logistic_order.delivered_date
                                    for logistic_order in self.logistic_info_all
                                ]
                            ),
                        ),
                        ('manufactures', self.manufactures),
                        ('products_shelf_type', self.products_shelf_type),
                        (
                            'sample_box',
                            sample_box,
                        ),
                        ('mix', mix),
                        (
                            'operation_time_t1',
                            int(operation_time_t1) if not sample_box else '',
                        ),
                        ('production_time', int(production_time)),
                        ('production_time_diff', int(production_time_diff)),
                        (
                            'logistic_time',
                            int(logistic_time) if logistic_time else logistic_time,
                        ),
                        (
                            'operation_time_t2',
                            int(operation_time_t2) if not sample_box else '',
                        ),
                        ('total_time_full', int(total_time_full)),
                        ('week', delivered.isocalendar().week if delivered else ''),
                        ('country', order.country.lower()),
                        ('carrier', carrier),
                        ('carrier_type', carrier_type),
                        ('logistic_operation_time', int(logistic_operation_time)),
                    ]
                )
                all_data.append(data)
        self.send_data_to_big_query(all_data)

    def multi_item_orders(
        self,
        order,
    ):
        production_time = self.count_days(
            self.operations_product_produced_date_by_prod_date[0][
                1
            ].batch.created_at.date(),
            self.operations_product_produced_date_by_prod_date[0][0][
                -1
            ].changed_at.date(),
        )
        production_time_diff = self.count_days(
            order.estimated_delivery_time.date(),
            self.operations_product_produced_date_by_prod_date[0][0][
                -1
            ].changed_at.date(),
        )
        operation_time_t1 = self.count_days(
            order.paid_at.date(),
            self.products[-1].batch.created_at.date(),
        )
        operation_time_t2 = self.count_days(
            self.operations_product_produced_date_by_prod_date[0][0][
                -1
            ].changed_at.date(),
            self.logistic_info_all[-1].delivered_date,
        )
        logistic_operation_time = self.count_days(
            self.logistic_info_all[0].sent_to_customer,
            self.logistic_info_all[-1].delivered_date,
        )
        total_time_full = self.count_days(
            order.paid_at.date(),
            self.logistic_info_all[-1].delivered_date,
        )
        self.manufactures = ', '.join(self.manufactures)
        self.logistic_type = ', '.join(self.logistic_type)
        self.products_shelf_type = ', '.join(self.products_shelf_type)
        carriers = [logistic_info.carrier for logistic_info in self.logistic_info_all]
        if len(carriers) == 1:
            carrier = carriers[0]
        else:
            carrier = 'Mix'
        logistic_time = None
        return (
            carrier,
            logistic_time,
            operation_time_t1,
            operation_time_t2,
            production_time,
            production_time_diff,
            total_time_full,
            logistic_operation_time,
        )

    def one_item_order_without_sample_box(
        self,
        logistic_time,
        order,
    ):
        production_time = self.count_days(
            self.operations_product_produced_date_by_prod_date[0][
                1
            ].batch.created_at.date(),
            self.operations_product_produced_date_by_prod_date[0][0][
                -1
            ].changed_at.date(),
        )
        production_time_diff = self.count_days(
            order.estimated_delivery_time.date(),
            self.operations_product_produced_date_by_prod_date[0][0][
                -1
            ].changed_at.date(),
        )
        operation_time_t1 = self.count_days(
            order.paid_at.date(),
            self.products[-1].batch.created_at.date(),
        )
        operation_time_t2 = self.count_days(
            self.operations_product_produced_date_by_prod_date[0][0][
                -1
            ].changed_at.date(),
            self.logistic_info_all[0].delivered_date,
        )
        logistic_operation_time = self.count_days(
            self.logistic_info_all[0].sent_to_customer,
            self.logistic_info_all[0].delivered_date,
        )
        total_time_full = self.count_days(
            order.paid_at.date(),
            self.logistic_info_all[0].delivered_date,
        )
        self.manufactures = self.manufactures.pop()
        self.logistic_type = self.logistic_type.pop()
        self.products_shelf_type = self.products_shelf_type.pop()
        logistic_time = logistic_time[0] if logistic_time else None
        return (
            logistic_time,
            operation_time_t1,
            operation_time_t2,
            production_time,
            production_time_diff,
            total_time_full,
            logistic_operation_time,
        )

    def send_data_to_big_query(self, all_data):
        ids = [data['id'] for data in all_data]
        client = bigquery.Client()
        query = (
            (
                f'delete FROM `tylko-bi-200712.{DATA_SPACE}.{DATA_TABLE}` '
                f'where id in {ids}'
            )
            .replace('[', '(')
            .replace(']', ')')
        )
        client.query(
            query,
            location='EU',
        )
        upload_list_to_big_query(
            DATA_SPACE,
            DATA_TABLE,
            all_data,
            write=bigquery.WriteDisposition.WRITE_APPEND,
        )

    def filter_data(
        self,
        logistic_orders_by_order: dict[str, list[LogisticOrderLeadTimeDTO]],
        order: Order,
    ):
        serializer = LogisticOrderLeadTimeDTODeserializer(
            data=logistic_orders_by_order[str(order.id)], many=True
        )
        serializer.is_valid(raise_exception=True)
        self.logistic_info_all = serializer.validated_data

        self.products = list(set(order.product_set.all()))
        self.manufactures = set(
            [
                product.manufactor.name
                for product in self.products
                if product.status != ProductStatus.ABORTED and product.manufactor
            ]
        )
        self.products = sorted(
            [
                product
                for product in self.products
                if product.status != ProductStatus.ABORTED and product.batch
            ],
            key=lambda x: x.batch.created_at,
        )
        self.logistic_info_all = list(
            filter(
                lambda x: x.delivered_date and x.order_type and x.sent_to_customer,
                self.logistic_info_all,
            )
        )
        self.products_shelf_type = set([p.cached_shelf_type for p in self.products])
        abort_in_order = [
            False,
        ]
        for product in self.products:
            abort_in_order = abort_in_order + (
                [
                    history.status == ProductStatus.ABORTED
                    for history in product.product_status_history.all()
                ]
            )
        self.operations_product_produced_date_by_prod_date = [
            (
                list(
                    filter(
                        lambda product_data: product_data
                        and product_data.status == ProductStatus.TO_BE_SHIPPED,
                        product.product_status_history.all(),
                    )
                ),
                product,
            )
            for product in self.products
        ]
        self.operations_product_produced_date_by_prod_date = sorted(
            self.operations_product_produced_date_by_prod_date,
            key=lambda op: op[0][0].changed_at
            if op[0]
            else datetime.datetime(1970, 1, 1),
        )
        self.logistic_type = [
            logistic_order.order_type for logistic_order in self.logistic_info_all
        ]
        return

    def get_data(self, start_date):
        select_related = [
            'region',
        ]
        prefetch_related = [
            'items',
            'product_set',
            'product_set__manufactor',
            'product_set__batch',
            'product_set__product_status_history',
        ]
        logistic_order_api_client = LogisticOrderAPIClient()
        order_ids = logistic_order_api_client.filter_kpi_leadtime_orders(start_date)

        orders = (
            Order.objects.filter(paid_at__isnull=False, id__in=order_ids)
            .select_related(*select_related)
            .prefetch_related(*prefetch_related)
            .exclude(order_type=OrderType.COMPLAINT)
            .order_by('id')
            .distinct('pk')
        )
        return orders

    def handle(self, *args, **options):
        start_date = (datetime.date.today() - datetime.timedelta(days=30)).replace(
            day=1
        )
        self.calculate(start_date)
